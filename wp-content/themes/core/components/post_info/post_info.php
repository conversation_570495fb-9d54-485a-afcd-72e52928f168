<?php
declare( strict_types=1 );

use \Tribe\Project\Templates\Components\post_info\Post_Info_Controller;
use Tribe\Project\Post_Types\Member_Stream\Member_Stream;

/**
 * @var array $args Arguments passed to the template
 */
// phpcs:ignore VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable
$c = Post_Info_Controller::factory( $args );

$conditional_styles = '';

$conditional_styles = $c->get_layout();

if ( ! $c->get_show_author() ) {
	$conditional_styles .= ' hide-author';
}

if ( ! $c->get_show_date() ) {
	$conditional_styles .= ' hide-date';
}

if ( count( $c->get_multiple_authors() ) > 0 ) {
	$conditional_styles .= ' c-post-info--multiple-authors';
}
?>
<div class="c-post-info <?php echo $conditional_styles; ?>">
	<?php if ( ! wp_is_mobile() && $c->get_show_author() && $c->get_show_avatar() ) : ?>
		<div class="c-post-info__avatars-wrapper">
			<?php if ( is_singular( Member_Stream::NAME ) ) : ?>
				<?php if ( $c->get_host_id() ) : ?>
					<?php get_template_part( 'components/image/image', null, $c->get_avatar_by_id( $c->get_host_id() ) ); ?>
				<?php endif; ?>
			<?php else : ?>
				<?php get_template_part( 'components/image/image', null, $c->get_avatar() ); ?>
			<?php endif; ?>

			<?php if ( count( $c->get_multiple_authors() ) > 0 ) :
				foreach ( $c->get_multiple_authors() as $author ) :
					if ( is_array( $author ) ) {
						get_template_part( 'components/image/image', null, $c->get_manual_avatar( $author ) );
					} else {
						get_template_part( 'components/image/image', null, $c->get_avatar_by_id( $author ) );
					}
				endforeach;
			endif; ?>
		</div>
	<?php endif; ?>

	<?php if ( $c->get_layout() == Post_Info_Controller::LAYOUT_ONE_LINE ) : ?>

		<?php if ( $c->get_show_author() ) : ?>
			<div class="c-post-info__author-container">
				<?php if ( $links = $c->get_authors_links() ) : ?>
					<?php echo $c->get_prefix(); ?>
					<?php echo $links; ?>
				<?php endif; ?>

				<?php if ( $c->get_show_date() && ! $c->get_show_modified_date() ) : ?>
					<span class="c-post-info__post-date-container">
						<span class="c-post-info__publishing-date"
							itemprop="datePublished" content="<?php echo $c->get_date( 'Y-m-d' ); ?>">
							on <?php echo $c->get_date( 'M j, Y' ); ?>
						</span>
				<?php endif; ?>

				<?php if ( $c->get_show_date() && $c->get_show_modified_date() && $c->get_modified_date() !== $c->get_date() ) : ?>
					<?php echo $c->get_divider(); ?>
					<span class="c-post-info__publishing-date c-post-info__publishing-date--modified"
						itemprop="dateModified" content="<?php echo $c->get_modified_date( 'Y-m-d' ); ?>">
						<?php echo sprintf( "%s %s", __( 'Last updated on', 'tribe' ), $c->get_modified_date( 'M j, Y' ) ); ?>
					</span>
				<?php endif; ?>

				<?php if ( $c->get_show_date() ) : ?>
					</span>
				<?php endif; ?>
			</div>
		<?php endif; ?>

	<?php endif; ?>

	<?php if ( $c->get_layout() == Post_Info_Controller::LAYOUT_TWO_LINES ) : ?>

		<?php if ( $c->get_show_author() || $c->get_show_date() ) : ?>
			<div class="c-post-info__meta-container">
		<?php endif; ?>

			<?php if ( $c->get_show_author() || $c->get_show_date() ) : ?>
				<div class="c-post-info__meta-content c-post-info__meta-content--authors">
					<?php if ( $c->get_show_author() ) : ?>
						<span class="c-post-info__author-container">
							<?php echo $c->get_prefix(); ?>
							<?php echo $c->get_authors_links(); ?>
						</span>
					<?php endif; ?>

					<?php if ( $c->get_show_date() && ! $c->get_show_modified_date() ) : ?>
						<span class="c-post-info__publishing-date"
							itemprop="datePublished" content="<?php echo $c->get_date( 'Y-m-d' ); ?>">
							on <?php echo $c->get_date( 'M j, Y' ); ?>
						</span>
					<?php endif; ?>
				</div>
			<?php endif; ?>

			<?php if ( $c->get_show_date() && $c->get_show_modified_date() ) : ?>
				<div class="c-post-info__meta-content">
					<span class="c-post-info__publishing-date c-post-info__publishing-date--modified"
						itemprop="dateModified" content="<?php echo $c->get_modified_date( 'Y-m-d' ); ?>">
						<?php echo sprintf( "%s %s", __( 'Last updated on', 'tribe' ), $c->get_modified_date( 'M j, Y' ) ); ?>
					</span>
				</div>
			<?php endif; ?>

		<?php if ( $c->get_show_author() || $c->get_show_date() ) : ?>
			</div>
		<?php endif; ?>
	<?php endif; ?>

</div>
