.c-post-info {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8px;
	margin-bottom: var(--spacer-30);

	@media(--viewport-medium-max) {
		flex-direction: row !important;
		align-items: flex-start;
	}

	&__avatars-wrapper {
		display: none;

		@media (--viewport-small) {
			display: flex;
		}
	}

	.c-image.item-single__author-image {
		margin-right: 0;

		&:not(:first-child) {
			margin-left: -15px;
		}
	}

	.c-image__image.item-single__author-image-img {
		width: 24px;
		height: 24px;
		object-fit: cover;
	}

	&__author-container {
		font-size: var(--font-size-heading-xxsmall);
		text-align: center;

		.c-post-info.layout--one-line &,
		.post-template-single-template_editorial & {
			font-size: 14px;
		}

		.post-template-single-template_editorial & {
			text-align: left;
		}

		@media (--viewport-medium) {
			.c-post-info.layout--one-line & {
				display: block;
			}
		}

		a.c-post-info__author-name {
			font-weight: var(--font-weight-semibold);
			transition: var(--transition);

			&:hover,
			&:focus {
				color: var(--color-primary-70);
				text-decoration: underline;
			}
		}
	}

	&__author-data  {
		display: inline-block;
	}

	&__post-date-container {
		display: inline;
	}

	&__meta {
		&-container {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			gap: 8px;

			@media (--viewport-full) {
				display: block;
			}
		}

		&-content {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			gap: 8px;

			@media (--viewport-full) {
				display: block;
			}
		}
	}

	&__publishing-date {
		text-align: center;
		&--modified {
			color: var(--color-neutral-50);

			.c-post-info--multiple-authors & {
				@media (--viewport-small-max) {
					display: block;
				}
			}


			.has-dark-background-color & {
				color: var(--color-white);
			}
		}
	}

	&__divider {
		color: var(--color-secondary-50);
		margin-right: var(--spacer-10);
    	margin-left: 3px;
	}

	&--multiple-authors {
		.item-single__author-image {
			&:not(:first-child) {
				margin-left: -5px;
			}

			@media(--viewport-medium-max) {
				display: block;
			}
		}
	}
}
