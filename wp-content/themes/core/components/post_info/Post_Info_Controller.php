<?php

namespace Tribe\Project\Templates\Components\post_info;

use DateTime;
use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Templates\Components\author\Author_Controller;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Object_Meta\Member_Stream_Meta;
use Tribe\Project\Object_Meta\User_Meta;
use Tribe\Project\Post_Types\Media\Media;
use Tribe\Project\Theme\Config\Image_Sizes;
use Tribe\Project\Post_Types\Member_Stream\Member_Stream;

/**
 * Class Post Info
 */
class Post_Info_Controller extends Abstract_Controller {
	public const PREFIX           = 'prefix';
	public const DIVIDER          = 'divider';
	public const DIVIDER_CLASSES  = 'divider_classes';
	public const SHOW_AUTHOR      = 'show_author';
	public const SHOW_AVATAR      = 'show_avatar';
	public const SHOW_DATE        = 'show_date';
	public const AUTHOR           = 'author';
	public const AUTHOR_ARGS      = 'author_args';
	public const MULTIPLE_AUTHORS = 'multiple_authors';
	public const LAYOUT           = 'layout';
	public const LAYOUT_ONE_LINE  = 'layout--one-line';
	public const LAYOUT_TWO_LINES = 'layout--two-lines';

	private string            $prefix;
	private string            $divider;
	private array             $divider_classes;
	private bool              $show_author;
	private bool              $show_avatar;
	private bool              $show_date;
	private Author_Controller $author;
	private string            $layout;
	private array             $multiple_authors;

	public function __construct( array $args = [] ) {
		$args                   = $this->parse_args( $args );
		$this->prefix           = (string) $args [ self::PREFIX ];
		$this->divider          = (string) $args [ self::DIVIDER ];
		$this->divider_classes  = (array) $args [ self::DIVIDER_CLASSES ];
		$this->show_author      = (bool) $args [ self::SHOW_AUTHOR ];
		$this->show_avatar      = (bool) $args [ self::SHOW_AVATAR ];
		$this->show_date        = (bool) $args [ self::SHOW_DATE ];
		$this->author           = Author_Controller::factory( $args [ self::AUTHOR_ARGS ] );
		$this->layout           = (string) $args [ self::LAYOUT ];

		if ( array_key_exists( self::MULTIPLE_AUTHORS, $args ) ) {
			$this->multiple_authors = (array) $args[ self::MULTIPLE_AUTHORS ] ?? [];
		} else {
			$this->multiple_authors = [];
		}
	}

	protected function defaults(): array {
		return [
			self::PREFIX           => '',
			self::DIVIDER          => '&bull;',
			self::DIVIDER_CLASSES  => [],
			self::SHOW_AUTHOR      => false,
			self::SHOW_AVATAR      => false,
			self::SHOW_DATE        => false,
			self::LAYOUT           => self::LAYOUT_ONE_LINE,
			self::MULTIPLE_AUTHORS => [],
		];
	}

	protected function required(): array {
		return [
			self::DIVIDER_CLASSES => [ 'c-post-info__divider' ],
		];
	}

	public function get_layout(): string {
		return $this->layout;
	}

	public function get_show_author(): bool {
		return $this->show_author;
	}

	public function get_show_avatar(): bool {
		return $this->show_avatar;
	}

	public function get_show_date(): bool {
		return $this->show_date;
	}

	public function get_multiple_authors(): array {
		return $this->multiple_authors;
	}

	public function get_show_modified_date(): bool {
		if ( ! $this->get_date() || ! $this->get_modified_date() ) {
			return false;
		}

		$created_date = new DateTime( get_the_date('c') );
		$modified_date = new DateTime( get_the_modified_date( 'c' ) );
		$interval = $created_date->diff( $modified_date );

		return $interval->format( '%a' ) > 1;
	}

	public function get_prefix(): string {
		if ( empty( $this->prefix ) ) {
			return '';
		}

		return sprintf(
			'%s ',
			$this->prefix
		);
	}

	public function get_author( $id = 0 ): string {
		if ( $id ) {
			return esc_html( $this->author->get_author_display_name( $id ) );
		}

		return esc_html( $this->author->get_author_name() );
	}

	public function get_author_archive_permalink( $id = 0 ): string {
		if ( $id ) {
			return esc_html( $this->author->get_author_link( $id ) );
		}

		return esc_url( $this->author->get_author_link() );
	}

	public function get_avatar_by_id( $avatar_id ): array {
		return [
			Image_Controller::IMG_URL      => esc_url( $this->author->get_author_avatar_from_id( $avatar_id ) ),
			Image_Controller::LINK_URL     => esc_url( $this->author->get_author_link( $avatar_id ) ),
			Image_Controller::LINK_ID      => 'item-single__author-image',
			Image_Controller::AS_BG        => false,
			Image_Controller::USE_LAZYLOAD => true,
			Image_Controller::CLASSES      => [ 'item-single__author-image' ],
			Image_Controller::IMG_ALT_TEXT => esc_attr( $this->author->get_author_display_name( $avatar_id ) ),
			Image_Controller::IMG_CLASSES  => [ 'item-single__author-image-img' ],
			Image_Controller::SRC_SIZE     => Image_Sizes::SQUARE_XSMALL,
		];
	}

	public function get_manual_avatar( $author ): array {
		if ( ! $author ) {
			return [];
		}

		return [
			Image_Controller::IMG_URL      => $author[ Member_Stream_Meta::GUEST_IMAGE ],
			Image_Controller::LINK_URL     => esc_url( $author[ Author_Controller::LINK_WEBSITE ] ) ?? '',
			Image_Controller::AS_BG        => false,
			Image_Controller::USE_LAZYLOAD => true,
			Image_Controller::WRAPPER_TAG  => 'div',
			Image_Controller::CLASSES      => [ 'item-single__author-image' ],
			Image_Controller::IMG_ALT_TEXT => esc_attr( $author[ Member_Stream_Meta::GUEST_NAME ] ),
			Image_Controller::IMG_CLASSES  => [ 'item-single__author-image-img' ],
			Image_Controller::SRC_SIZE     => Image_Sizes::SQUARE_XSMALL,
		];
	}

	public function get_avatar(): array {
		return $this->author->get_author_image();
	}

	public function get_date( string $format = 'F j, Y' ): string {
		return get_the_date( $format );
	}

	public function get_modified_date( string $format = 'F j, Y' ): string {
		return get_the_modified_date( $format );
	}

	public function get_divider(): string {
		return sprintf(
			'<span %s>%s</span>',
			Markup_Utils::class_attribute( $this->divider_classes ),
			$this->divider
		);
	}

	public function get_author_tooltip( $id = 0 ): ?string {
		if ( ! $id ) {
			$id = $this->author->get_author_id();
		}

		$author_description_content = ( new Author_Controller( [ Author_Controller::AUTHOR_ID => $id ] ) )->get_author_description();

		if ( ! preg_match( '/^<p>/', $author_description_content ) ) {
			$author_description_content = '<p>' . $author_description_content . '</p>';
		}


		$author_description = ! empty( $author_description_content ) ? '<div class="c-tooltip__description">' . $author_description_content . '</div>' : '';
		$author_name        = Author_Controller::get_author_display_name( $id );
		$first_name         = Author_Controller::get_the_author_meta( 'first_name', $id );
		$author_title       = Author_Controller::get_the_author_meta( 'user_title', $id );
		$author_link        = defer_template_part( 'components/link/link', null, [
			Link_Controller::URL     => get_author_posts_url( $id ),
			Link_Controller::CONTENT => __( 'More about ' . $first_name, 'tribe' ),
			Link_Controller::TARGET  => '_self',
			Link_Controller::CLASSES => [
				'a-cta',
			],
		] );
		$author_avatar      = defer_template_part( 'components/image/image', null, [
			Image_Controller::IMG_URL      => esc_url( Author_Controller::get_author_avatar_from_id( $id, 64 ) ),
			Image_Controller::LINK_URL     => esc_url( get_author_posts_url( $id ) ),
			Image_Controller::LINK_ID      => 'item-single__author-image',
			Image_Controller::AS_BG        => true,
			Image_Controller::USE_LAZYLOAD => true,
			Image_Controller::CLASSES      => [ 'item-single__author-image' ],
			Image_Controller::IMG_ALT_TEXT => esc_attr( $author_name ),
			Image_Controller::IMG_CLASSES  => [ 'item-single__author-image-img' ],
		] );

		$tooltip        = defer_template_part( 'components/tooltip/tooltip', null, [
			'attrs'   => [
				'itemprop' => 'authorInformation',
			],
			'content' => '<div class="c-tooltip__author-info-wrapper">' . $author_avatar . '<div class="c-tooltip__author-info-wrapper--details"><span>' . $author_name . '</span> <span>' . $author_title . '</span></div></div>' . $author_description . $author_link,
		] );
		$author_tooltip = '<div class="c-post-info__author-data" data-js="tooltip-trigger"><span class="c-post-info__author-name">' . $author_name . '</span>' . $tooltip . '</div>';

		return $author_tooltip;
	}

	private function get_author_description( $id ): string {
		$descriptions = get_field( User_Meta::DESCRIPTION, 'user_' . (int) $id );

		if ( ! $descriptions ) {
			return Author_Controller::get_the_author_meta( 'user_description', (int) $id );
		}

		$description = array_filter(
			$descriptions,
			function ( $description ) {
				if ( function_exists( 'pll_current_language' ) ) {
					return
						(int) $description[ User_Meta::DESCRIPTION_SITE ] === get_current_blog_id() &&
						$description[ User_Meta::DESCRIPTION_LANGUAGE ] === pll_current_language();
				}

				return
					(int) $description[ User_Meta::DESCRIPTION_SITE ] === get_current_blog_id() &&
					$description[ User_Meta::DESCRIPTION_LANGUAGE ] === User_Meta::DEFAULT_LANGUAGE;
			}
		);

		if ( ! $description ) {
			return Author_Controller::get_the_author_meta( 'user_description', (int) $id );
		}

		return '<p>' . wp_kses(
			current( $description )[ User_Meta::DESCRIPTION_CONTENT ],
			[
				'a'      => [
					'href'  => [],
					'title' => [],
				],
				'br'     => [],
				'em'     => [],
				'strong' => [],
			]
		) . '</p>';
	}

	private function show_tooltip(): bool {
		return is_page_template( 'single-template_perfect_listicle.php' ) || is_page_template( 'single-template_perfect_post.php' );
	}

	public function get_host_id(): int {
		if ( is_singular( Member_Stream::NAME ) && $host = get_field( Member_Stream_Meta::HOST ) ) {
			return $host[ 'ID' ];
		}

		return 0;
	}

	public function get_authors_links(): string {
		$authors_count = count( $this->get_multiple_authors() );

		if ( is_singular( Member_Stream::NAME ) ) {
			$host = get_field( Member_Stream_Meta::HOST );
			$result = '';

			if ( $host ) {
				$result = sprintf(
					'<a href="%s" class="c-post-info__author-name">%s</a>',
					$this->get_author_archive_permalink( $host[ 'ID' ] ),
					$host[ 'display_name' ],
				);
			}
		} else {
			if ( $this->show_tooltip() ) {
				$result = $this->get_author_tooltip();
			} else {
				$result = sprintf(
					'<a href="%s" class="c-post-info__author-name">%s</a>',
					$this->get_author_archive_permalink(),
					$this->get_author(),
				);
			}
		}

		if ( $authors_count > 0 ) {
			if ( $authors_count === 1 ) {
				$author = $this->get_multiple_authors()[0];

				if ( is_array( $this->get_multiple_authors()[0] ) ) {
					if ( ! empty( $author[ Author_Controller::LINK_WEBSITE ] ) ) {
						$result .= sprintf(
							' & <a href="%s" class="c-post-info__author-name">%s</a>',
							$author[ Author_Controller::LINK_WEBSITE ],
							$author[ Member_Stream_Meta::GUEST_NAME ],
						);
					} else {
						if ( $this->get_host_id() ) {
							$result .= ' & ';
						}
						$result .= '<span class="c-post-info__author-name">' . $author[ Member_Stream_Meta::GUEST_NAME ] . '</span>';
					}
				} else {
					if ( $this->show_tooltip() ) {
						$result .= sprintf(
							' & %s',
							$this->get_author_tooltip( $author ),
						);
					} else {
						if ( is_singular([ Media::NAME ] ) ) {
							$result .= sprintf(
								' featuring <a href="%s" class="c-post-info__author-name">%s</a>',
								$this->get_author_archive_permalink( $author ),
								$this->get_author( $author ),
							);
						} else {
							$result .= sprintf(
								' & <a href="%s" class="c-post-info__author-name">%s</a>',
								$this->get_author_archive_permalink( $author ),
								$this->get_author( $author ),
							);
						}
					}
				}
			} else {
				foreach ( $this->get_multiple_authors() as $index => $author ) {
					if ( $index === 0 && is_singular([ Media::NAME ] ) ) {
						$result .= ' featuring ';
					} elseif ( $index === $authors_count - 1 ) {
						$result .= ' & ';
					} else {
						$result .= ', ';
					}

					if ( is_array( $author ) ) {
						if ( ! empty( $author[ Author_Controller::LINK_WEBSITE ] ) ) {
							$result .= sprintf(
								'<a href="%s" class="c-post-info__author-name">%s</a>',
								$author[ Author_Controller::LINK_WEBSITE ],
								$author[ Member_Stream_Meta::GUEST_NAME ],
							);
						} else {
							$result .= '<span class="c-post-info__author-name">' . $author[ Member_Stream_Meta::GUEST_NAME ] . '</span>';
						}
					} else {
						if ( $this->show_tooltip() ) {
							$result .= $this->get_author_tooltip( $author );
						} else {
							$result .= sprintf(
								'<a href="%s" class="c-post-info__author-name">%s</a>',
								$this->get_author_archive_permalink( $author ),
								$this->get_author( $author ),
							);
						}
					}
				}
			}
		}

		return $result;
	}
}
