<?php
declare( strict_types=1 );

use Tribe\Project\Templates\Components\author\Author_Controller;

/**
 * @var array $args Arguments passed to the template
 */
$c = Author_Controller::factory( $args );

$author_about = $c->get_author_about();

?>

<div class="item-single__author-container">
	<h3 class="item-single__author-type"><?php _e( 'Host', 'tribe' ); ?></h3>

	<div class="item-single__author-infos">
		<?php get_template_part( 'components/image/image', null, $c->get_author_image() ); ?>

		<div class="item-single__author-details">
			<div class="item-single__author-name">
				<span class="item-single__author-name-placeholder">
					<?php _e( 'By', 'tribe' ); ?>
				</span>
				<a href="<?php echo esc_url( $c->get_author_link() ); ?>" title="<?php echo esc_attr( $c->get_author_name() ); ?>">
					<?php echo esc_html( $c->get_author_name() ); ?>
				</a>
			</div>

			<?php if ( $c->get_author_job_title() ) { ?>
				<div class="item-single__author-job-title">
					<?php echo esc_html( $c->get_author_job_title() ); ?>
				</div>
			<?php } ?>

			<?php if ( $social_links = $c->show_links_list( true ) ) { ?>
				<div class="item-single__author-links">
					<?php echo $social_links; ?>
				</div>
			<?php } ?>
		</div>
	</div>

	<?php if ( $author_about ) { ?>
		<p class="item-single__author-about">
			<?php echo wp_kses_post( $author_about ); ?>
		</p>
	<?php } ?>

	<div class="item-single__author-actions">
		<?php if ( $author_about ) { ?>
			<div class="item-single__author-actions__action item-single__author-actions__see-more">
				<a href="#" title="<?php echo esc_attr( __( 'See More', 'tribe' ) ); ?>" data-expanded-text="<?php echo esc_attr( __( 'See Less', 'tribe' ) ); ?>" class="a-cta a-cta--secondary">
					<?php _e( 'See More', 'tribe' ); ?>
				</a>
			</div>
		<?php } ?>
		<div class="item-single__author-actions__action">
			<a href="<?php echo esc_url( $c->get_author_link() ); ?>" title="<?php echo esc_attr( __( 'View Author Page', 'tribe' ) ); ?>" class="a-cta a-cta--secondary">
				<?php _e( 'View Author Page', 'tribe' ); ?>
			</a>
		</div>
	</div>
</div>
