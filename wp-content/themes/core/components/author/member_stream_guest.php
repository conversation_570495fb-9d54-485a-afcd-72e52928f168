<?php
declare( strict_types=1 );

use Tribe\Project\Object_Meta\Member_Stream_Meta;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\author\Author_Controller;

/**
 * @var array $args Arguments passed to the template
 */

// todo: move this into a controller?
$img_args = [
	Image_Controller::IMG_URL      => esc_url( $args[ Member_Stream_Meta::GUEST_IMAGE ] ) ?? 0,
	Image_Controller::AS_BG        => true,
	Image_Controller::USE_LAZYLOAD => true,
	Image_Controller::CLASSES      => [ 'item-single__author-image' ],
	Image_Controller::IMG_ALT_TEXT => esc_attr( $args[ Member_Stream_Meta::GUEST_NAME ] ) ?? '',
	Image_Controller::IMG_CLASSES  => [ 'item-single__author-image-img' ],
	Image_Controller::LINK_URL     => esc_url( $args[ Member_Stream_Meta::GUEST_URL ] ) ?? '',
];

$c = Author_Controller::factory( [
	Author_Controller::SHOW_LINKS_LIST => $args[ Member_Stream_Meta::GUEST_LINKS ]
] );

?>

<div class="item-single__author-container">
	<h3 class="item-single__author-type"><?php _e( 'Guest', 'tribe' ); ?></h3>

	<div class="item-single__author-infos">
		<?php if ( ! empty( $args[ Member_Stream_Meta::GUEST_IMAGE] ) ) { ?>
			<?php get_template_part( 'components/image/image', null, $img_args ); ?>
		<?php } ?>

		<div class="item-single__author-details">
			<?php if ( ! empty( $args[ Member_Stream_Meta::GUEST_NAME ] ) ) { ?>
				<div class="item-single__author-name">
					<span class="item-single__author-name-placeholder">
						<?php _e( 'By', 'tribe' ); ?>
					</span>
					<?php if ( ! empty( $args[ Member_Stream_Meta::GUEST_URL ] ) ) { ?>
						<a href="<?php echo esc_url( $args[ Member_Stream_Meta::GUEST_URL ] ); ?>" title="<?php echo esc_attr( $args[ Member_Stream_Meta::GUEST_NAME ] ); ?>">
							<?php echo esc_html( $args[ Member_Stream_Meta::GUEST_NAME ] ); ?>
						</a>
					<?php } else { ?>
						<?php echo esc_html( $args[ Member_Stream_Meta::GUEST_NAME ] ); ?>
					<?php } ?>
				</div>
			<?php } ?>

			<?php if ( ! empty( $args[ Member_Stream_Meta::GUEST_TITLE ] ) ) { ?>
				<div class="item-single__author-job-title">
					<?php echo esc_html( $args[ Member_Stream_Meta::GUEST_TITLE ] ); ?>
				</div>
			<?php } ?>

			<?php if ( $social_links = $c->show_links_list( true ) ) { ?>
				<div class="item-single__author-links">
					<?php echo $social_links; ?>
				</div>
			<?php } ?>
		</div>
	</div>

	<?php if ( ! empty( $args[ Member_Stream_Meta::GUEST_BIO ] ) ) { ?>
		<p class="item-single__author-about">
			<?php echo wp_kses_post( $args[ Member_Stream_Meta::GUEST_BIO ] ); ?>
		</p>
	<?php } ?>

	<div class="item-single__author-actions">
		<?php if ( ! empty( $args[ Member_Stream_Meta::GUEST_BIO ] ) ) { ?>
			<div class="item-single__author-actions__action item-single__author-actions__see-more">
				<a href="#" title="<?php echo esc_attr( __( 'See More', 'tribe' ) ); ?>" data-expanded-text="<?php echo esc_attr( __( 'See Less', 'tribe' ) ); ?>" class="a-cta a-cta--secondary">
					<?php _e( 'See More', 'tribe' ); ?>
				</a>
			</div>
		<?php } ?>
		<?php if ( ! empty( $args[ Member_Stream_Meta::GUEST_URL ] ) ) { ?>
			<div class="item-single__author-actions__action">
				<a href="<?php echo esc_url( $args[ Member_Stream_Meta::GUEST_URL ] ); ?>" title="<?php echo esc_attr( __( 'View Author Page', 'tribe' ) ); ?>" class="a-cta a-cta--secondary">
					<?php _e( 'View Author Page', 'tribe' ); ?>
				</a>
			</div>
		<?php } ?>
	</div>
</div>
