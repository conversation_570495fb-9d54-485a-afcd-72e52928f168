<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\columns;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Blocks\Types\Columns\Columns;

class Columns_Block_Controller extends Abstract_Controller {
	public const CONTAINER_CLASSES  = 'container_classes';
	public const CLASSES            = 'classes';
	public const ATTRS              = 'attrs';
	public const COLUMNS_NUMBER     = 'columns_number';
	public const GRID_OPTION        = 'grid_option';
	public const VERTICAL_ALIGN	 	= 'vertical_align';
	public const MOBILE_BEHAVIOUR  	= 'mobile_behaviour';
	public const TITLE 				= 'title';
	public const HEADING_TAG 		= 'heading_tag';
	public const CONTENT 			= 'content';
	public const BACKGROUND_COLOR 	= 'background_color';

	private array $container_classes;
	private array $classes;
	private array $attrs;
	private int $columns_number;
	private string $grid_option;
	private bool $vertical_align;
	private string $mobile_behaviour;
	private string $title;
	private string $heading_tag;
	private string $content;
	private string $background_color;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		$args = $this->parse_args( $args );

		$this->container_classes  = (array) $args[ self::CONTAINER_CLASSES ];
		$this->classes            = (array) $args[ self::CLASSES ];
		$this->attrs              = (array) $args[ self::ATTRS ];
		$this->columns_number     = (int) $args[ self::COLUMNS_NUMBER ];
		$this->grid_option        = (string) $args[ self::GRID_OPTION ];
		$this->vertical_align     = (bool) $args[ self::VERTICAL_ALIGN ];
		$this->mobile_behaviour   = (string) $args[ self::MOBILE_BEHAVIOUR ];
		$this->title              = (string) $args[ self::TITLE ];
		$this->heading_tag        = (string) $args[ self::HEADING_TAG ];
		$this->content            = (string) $args[ self::CONTENT ];
		$this->background_color   = (string) $args[ self::BACKGROUND_COLOR ];
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::CONTAINER_CLASSES  => [],
			self::CLASSES            => [],
			self::ATTRS              => [],
			self::COLUMNS_NUMBER     => 0,
			self::GRID_OPTION        => '',
			self::VERTICAL_ALIGN     => false,
			self::MOBILE_BEHAVIOUR   => '',
			self::TITLE              => '',
			self::HEADING_TAG        => '',
			self::CONTENT            => '',
			self::BACKGROUND_COLOR   => '',
		];
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [ 'l-container' ],
			self::CLASSES           => [ 'c-block', 'b-columns' ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		$this->classes[] = 'b-columns--' . $this->mobile_behaviour;

		if ( $this->background_color !== Columns::NO_BACKGROUND ) {
			$this->classes[] = 'b-columns--has-background';
			$this->classes[] = 'b-columns--background-' . $this->background_color;
		}

		if ( $this->is_vertically_aligned() ) {
			$this->classes[] = 'b-columns--vertically-aligned';
		}

		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	/**
	 * @return string
	 */
	public function get_grid_container_classes(): string {
		$classes = [  'b-columns__grid' ];

		if ( ! $this->grid_option || Columns::GRID_OPTION_12 === $this->grid_option ) {
			$classes[] = 'b-columns__grid--' . $this->get_grid_layout() .'-cols';
		} else {
			$classes[] = 'b-columns__grid--' . sanitize_title( str_replace( '_', '-', $this->grid_option ) );
		}

		if ( $this->is_mobile_behaviour_carousel() && wp_is_mobile() ) {
			$classes = [
				...$classes,
				'c-slider__main--has-pagination',
				'swiper-container',
				'swiper-container-horizontal',
			];
		}

		return Markup_Utils::class_attribute( $classes );
	}

	public function get_mobile_behaviour(): string {
		return self::$mobile_behaviour;
	}

	public function is_mobile_behaviour_carousel(): bool {
		return $this->mobile_behaviour === Columns::MOBILE_BEHAVIOUR_CAROUSEL;
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function get_title(): ?Deferred_Component {
		if ( ! $this->title ) {
			return null;
		}

		return defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => $this->heading_tag,
			Text_Controller::CLASSES => [
				'c-block__title',
				'b-columns__title',
				'h3',
			],
			Text_Controller::CONTENT => $this->title ?? '',
		] );
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function get_content(): ?Deferred_Component {
		if ( ! $this->content ) {
			return null;
		}

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [
				'c-block__description',
				'b-columns__description',
				't-sink',
				's-sink',
			],
			Container_Controller::CONTENT => $this->content ?? '',
		] );
	}

	/**
	 * @return int
	 */
	public function get_number_of_columns(): int {
		return $this->columns_number;
	}

	/**
	 * @return int
	 */
	public function get_grid_layout(): int {
		if ( $this->get_number_of_columns() > 3 || $this->get_number_of_columns() % 3 === 0 ) {
			return 3;
		} elseif ( $this->get_number_of_columns() % 2 === 0 ) {
			return 2;
		}

		return 1;
	}

	/**
	 * @return string
	 */
	public function get_inner_block_template(): ?string {
		$template = [];

		for ( $x = 0; $x < $this->get_number_of_columns(); $x++ ) {
			$template[] = [ 'acf/column' ];
		}

		return esc_attr( wp_json_encode( $template ) );
	}

	/**
	 * @return string
	 */
	public function get_background_color(): string {
		return $this->background_color;
	}

	public function is_vertically_aligned(): bool {
		return $this->vertical_align;
	}

}
