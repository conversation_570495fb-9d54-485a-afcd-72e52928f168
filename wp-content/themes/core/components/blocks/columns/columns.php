<?php
declare( strict_types=1 );

use Tribe\Project\Templates\Components\blocks\columns\Columns_Block_Controller;

/**
 * @var array $args Arguments passed to the template
 */
// phpcs:ignore VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable
$c = Columns_Block_Controller::factory( $args );
?>

<section <?php echo $c->get_classes(); ?> <?php echo $c->get_attrs(); ?>>
	<div <?php echo $c->get_container_classes();?>>
		<?php if ( $c->get_title() || $c->get_content() ) : ?>
			<div class="b-columns__header-wrapper">
				<?php echo $c->get_title(); ?>
				<?php echo $c->get_content(); ?>
			</div>
		<?php endif; ?>

		<?php if ( $c->is_mobile_behaviour_carousel() && wp_is_mobile() ) : ?>
			<div class="c-slider">
		<?php endif; ?>
			<InnerBlocks
				<?php echo $c->get_grid_container_classes(); ?>
				template="<?php echo $c->get_inner_block_template(); ?>"
				templateLock="false"
				directInsert="true"
			/>

		<?php if ( $c->is_mobile_behaviour_carousel() && wp_is_mobile() ) : ?>
			</div>
		<?php endif; ?>

	</div>
</section>
