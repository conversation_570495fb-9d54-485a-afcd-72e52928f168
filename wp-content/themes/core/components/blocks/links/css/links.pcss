/* -----------------------------------------------------------------------------
 *
 * Component: Block: Links
 *
 * ----------------------------------------------------------------------------- */

.b-links {

	.g-2-up,
	.g-3-up {

		& > * {
			@media (--viewport-medium) {
				width: 100%;
				margin: 0 var(--grid-gutter-small-half) var(--grid-gutter-small);
			}
		}
	}

	.g-2-up {

		& > * {
			@media (--viewport-full) {
				width: calc(50% - var(--grid-gutter));
				margin: 0 var(--grid-gutter-half) var(--grid-gutter);
			}
		}
	}

	.g-3-up {

		& > * {
			@media (--viewport-full) {
				width: calc(33.3333% - var(--grid-gutter));
				margin: 0 var(--grid-gutter-half) var(--grid-gutter);
			}
		}
	}

	.b-links__container {
		& > div {
			& > .b-links__content--continuous-count {
				counter-reset: continuouscount;

				~ .b-links__content--continuous-count {
					counter-reset: none;

					.b-links__list-title {
						display: none;
					}
				}

				&:has(.b-links__list-title) {

					~ .b-links__content--continuous-count {
						@media (--viewport-full) {
							margin-top: calc(23px + 24px);
						}
					}
				}
			}
		}
	}

	.b-links__content--continuous-count {
		&:not(:last-of-type) {
			@media (--viewport-medium-max) {
				margin-bottom: 0;
			}
		}

		& + .b-links__content {
			&:not(.b-links__content--continuous-count) {
				@media (--viewport-medium-max) {
					margin-top: var(--grid-gutter-small);
				}
			}
		}

		.b-links__list-item {
			counter-increment: continuouscount;
			list-style-type: none;

			&:before {
				content: counter(continuouscount, decimal-leading-zero) ".";
				display: inline;
				color: var(--color-black);
				font-family: var(--font-family-sans-serif);
				font-weight: var(--font-weight-bold);
				font-size: var(--font-size-body-small);
				line-height: 1.9;
				margin-left: 11px;
			}
		}
	}
}

/* -----------------------------------------------------------------------------
 * Links: Container
 * ----------------------------------------------------------------------------- */

.b-links__container {
	/* CASE: Layout Inline */
	.b-links--layout-inline & {
		@media (--viewport-full) {
			display: flex;
			justify-content: space-between;
		}
	}
}

/* -----------------------------------------------------------------------------
 * Links: Header
 * ----------------------------------------------------------------------------- */

.b-links__header {

	&.c-block__header.c-block__header--cta-end {
		align-self: flex-start;
	}

	.b-links & {
		margin-bottom: var(--spacer-50);

		h1 {
			&.b-links__title {
				@mixin t-display-small;

				@media(--viewport-full) {
					@mixin t-display-x-large;
				}
			}
		}
	}

	.b-links--2-col &,
	.b-links--3-col & {
		width: 100%;
	}

	.c-block__cta-link {

		.acf-block-preview & {
			color: #fff;
			text-decoration: none;
			pointer-events: none;
		}
	}
}

/* -----------------------------------------------------------------------------
 * Links: Title
 * ----------------------------------------------------------------------------- */

.b-links__title {
	margin-bottom: var(--spacer-20);
}

/* -----------------------------------------------------------------------------
 * Links: Description
 * ----------------------------------------------------------------------------- */

.b-links__description {

	&.c-content-block__content {
		margin-top: 0;
	}
}

/* -----------------------------------------------------------------------------
 * Links: Content
 * ----------------------------------------------------------------------------- */

.b-links__content {
	&:last-child {
		margin-bottom: 0;
	}

	@media (--viewport-full) {
		.b-links & {
			margin-bottom: 0;
		}
	}
}

/* -----------------------------------------------------------------------------
 * Links: List
 * ----------------------------------------------------------------------------- */

.b-links__list-title,
.t-sink > .wp-block-group .b-links__list-title {
	@mixin t-overline;

	margin-bottom: var(--spacer-30);
}

.b-links__list-title {
	.page #main-content > .l-sink > :first-child:is(.c-block):not(.b-card-grid):not(.b-content-loop):not(.b-content-columns) h2&,
	.page #main-content > .l-sink > :first-child:is(.wp-block-group) > .wp-block-group__inner-container > .c-block:first-of-type h2&,
	.page #main-content > .l-sink >.wp-block-group > .wp-block-group__inner-container > .c-block & {
		@mixin t-overline;
	}
}

.b-links__list,
.t-sink > .wp-block-group .b-links__list {
	list-style-type: none;
	padding-left: 0;

	&.c-block {
		margin: 0;
	}

	.s-sink > .wp-block-group.has-dark-background-color & {
		margin-top: 0;
	}

	&.list-type--ol {
		list-style-type: decimal-leading-zero;
		padding-left: 0;

		.acf-block-preview & {
			margin-left: 0;
		}

		li {
			position: relative;
			padding-left: var(--spacer-10);

			&:before {
				display: none;
			}
		}

		a.b-links__list-link {
			margin-left: 0;
		}
	}

	&.list-type--custom_icon {
		padding-left: 0;
	}

	&.list-type--checklist {
		.b-links__list-item {
			display: grid;
			grid-template-columns: var(--spacer-40) auto;
			grid-gap: var(--spacer-30);
		}
	}
}

.b-links__list-item {
	color: var(--color-black);
	font-family: var(--font-family-sans-serif);
	font-weight: var(--font-weight-bold);
	line-height: 1.7;
	margin-bottom: var(--spacer-30);
	margin-left: 29px;
	position: relative;

	@media (--viewport-full) {
		&:last-child {
			margin-bottom: 0;
		}
	}

	p {
		font-weight: var(--font-weight-bold);
	}

	&::marker {

		.has-dark-background-color & {
			color: var(--color-white);
		}
	}

	&:before {
		@mixin icon;

		position: absolute;
		left: -36px;
		top: 0;
		display: block;
		color: var(--color-black);
		content: var(--icon-chevron-right);
		font-family: var(--font-family-core-icons);
		font-size: var(--font-size-body-large);
		line-height: 1.4;
		text-align: center;
		transition: var(--transition);

		.has-dark-background-color & {
			color: var(--color-white);
		}
	}

	.list-type--custom_icon & {
		margin-left: 35px;

		&:before {
			background-image: var(--list-icon);
			background-position: center center;
			background-size: contain;
			background-repeat: no-repeat;
			content: "";
			height: 20px;
			left: -35px;
			top: 6px;
			width: 20px;
		}
	}

	.list-type--checklist & {
		display: flex;
		align-items: flex-start;
		list-style-type: none;
		margin-left: 0;

		&:before {
			content: var(--icon-check);
			position: initial;
			font-family: var(--font-family-core-icons);
			display: flex;
			align-items: center;
			justify-content: center;
			width: var(--spacer-40);
			height: var(--spacer-40);
			border-radius: 50%;
			background-color: var(--color-neutral-10);
			margin-right: var(--spacer-30);
			color: var(--global-color-primary);
			font-size: 28px;
			flex-shrink: 0;
		}

		&~ li {
			margin-top: var(--spacer-50);
		}
	}
}

.b-links__list-item p {
	font-weight: var(--font-weight-bold);

	@media (--viewport-medium) {
		font-size: var(--font-size-body);
		line-height: 1.4;
	}

	.wp-block-group & {
		font-weight: var(--font-weight-bold);
	}
}

.b-links__list-link {
	@mixin a-cta-secondary;

	display: inline;
	padding: 0;
	position: relative;
	transition: var(--transition);

	@media (--viewport-medium) {
		font-size: var(--font-size-body);
		line-height: 1.4;
	}

	&:hover,
	&:focus {
		box-shadow: none;
	}

	.has-dark-background-color &,
	.wp-block-group.has-dark-background-color & {
		&:hover,
		&:focus {
			box-shadow: none;
		}
	}

	.b-links & {
		text-decoration: none;
		font-weight: var(--font-weight-bold);
		color: var(--color-black);

		&:focus,
		&:hover {
			color: var(--color-black);
			border-color: transparent;
			border-width: 2px;
			text-decoration: none !important;
			box-shadow: 0 8px 0 0 var(--color-secondary);
		}
	}

	.t-sink .wp-block-group & {
		color: var(--color-black);

		&:focus,
		&:hover {
			color: var(--color-black);
		}
	}

	.has-dark-background-color & {
		color: var(--color-white);

		&:hover,
		&:focus {
			color: var(--color-white);
			border-color: var(--color-white);
		}
	}

	.acf-block-preview & {
		pointer-events: none;
	}
}
