<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\listicle\ssl_xvsy_video_comparison;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Admin\Editor\Blocks_Utilities;
use Tribe\Project\Templates\Components\ssl_XvsY_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\blocks\simple_embed\Simple_Embed_Block_Controller;

class ssl_XvsY_Video_Comparison_Block_Controller extends ssl_XvsY_Controller {
	public const CONTAINER_CLASSES = 'container_classes';
	public const CLASSES           = 'classes';
	public const ATTRS             = 'attrs';
	public const ROOT_CLASS        = 'b-ssl-xvsy-video-comparison';
	public const INTRODUCTION_TEXT = 'intro_text';
	public const VIDEO_THUMBNAIL   = 'video_thumbnail';
	public const VIDEO_URL         = 'video_url';
	public const LINKS_LIST        = 'links_list';
	public const SECTION_LINK      = 'section_link';

	private array  $container_classes;
	private array  $classes;
	private array  $attrs;
	private array  $links_list;
	private string $introduction_text;
	private int    $video_thumbnail;
	private string $video_url;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		parent::__construct( $args );

		$args = $this->parse_args( $args );

		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->introduction_text = (string) $args[ self::INTRODUCTION_TEXT ];
		$this->video_thumbnail   = (int) $args[ self::VIDEO_THUMBNAIL ];
		$this->video_url         = (string) $args[ self::VIDEO_URL ];
		$this->links_list        = (array) $args[ self::LINKS_LIST ] ?: [];
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [],
			self::ATTRS             => [],
		];
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [ 'c-block', self::ROOT_CLASS ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		if ( is_admin() ) {
			$this->attrs['data-toc-title'] = __( 'Video Comparison', 'tribe' );
		}

		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	/**
	 * @return string|null
	 */
	public function get_accordion_content(): ?string {
		if ( ! $this->video_url ) {
			return null;
		}

		$content = '';

		if ( $intro = $this->introduction_text ) {
			$content .= $intro;
		}

		$content .= defer_template_part( 'components/blocks/simple_embed/simple_embed', null, [
			Simple_Embed_Block_Controller::LABEL     => $this->get_provider_a()->get_name() . ' vs. ' . $this->get_provider_b()->get_name(),
			Simple_Embed_Block_Controller::CLASSES   => [ self::ROOT_CLASS . '__video' ],
			Simple_Embed_Block_Controller::THUMBNAIL => $this->video_thumbnail ?: 0,
			Simple_Embed_Block_Controller::LINK      => $this->video_url,
		] );

		if ( $this->links_list ) {
			$list_items = '';

			foreach ( $this->links_list as $section_link ) {
				$section_link = $section_link[ self::SECTION_LINK ];

				$link = defer_template_part( 'components/link/link', null, [
					Link_Controller::URL     => $section_link['url'],
					Link_Controller::CONTENT => $section_link['title'],
					Link_Controller::TARGET  => $section_link['target'],
				] );

				$list_items .= defer_template_part( 'components/container/container', null, [
					Container_Controller::TAG     => 'li',
					Container_Controller::CONTENT => $link,
				] );
			}

			$content .= defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'span',
				Text_Controller::CLASSES => [ 't-overline' ],
				Text_Controller::CONTENT => __( 'SEE MORE DETAILS', 'tribe' ),
			] );

			$content .= defer_template_part( 'components/container/container', null, [
				Container_Controller::TAG     => 'ul',
				Container_Controller::CONTENT => $list_items,
			] );
		}

		return $content;
	}

	/**
	 * @return string
	 */
	public function get_accordion_title(): string {
		return $this->get_provider_a()->get_name() . ' Vs. ' . $this->get_provider_b()->get_name() . ' Video Comparison';
	}

	/**
	 * @return string|null
	 */
	public function get_no_content_error(): ?string {
		return Blocks_Utilities::format_admin_error_message( 'No video url submitted. The block will not be rendered.' );
	}
}
