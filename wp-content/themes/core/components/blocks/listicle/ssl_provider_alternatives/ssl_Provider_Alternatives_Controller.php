<?php declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\listicle\ssl_provider_alternatives;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Admin\Editor\Blocks_Utilities;
use Tribe\Project\Templates\Components\ssl_Provider_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\rating\Rating_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\Traits\Handles_SSL_Premium_Listing;

class ssl_Provider_Alternatives_Controller extends ssl_Provider_Controller {
	use Handles_SSL_Premium_Listing;

	public const CLASSES              = 'classes';
	public const ATTRS                = 'attrs';
	public const CONTAINER_CLASSES    = 'container_classes';
	public const CONTENT_CLASSES      = 'content_classes';
	public const DESCRIPTION          = 'description';
	public const ALTERNATIVES_QTY     = 4;
	public const DATALAYER_BLOCK_NAME = 'provider-alternatives';

	private array  $classes;
	private array  $attrs;
	private array  $container_classes;
	private array  $content_classes;
	private string $description;

	public function __construct( array $args = [] ) {
		parent::__construct( $args );

		$args                    = $this->parse_args( $args );
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->content_classes   = (array) $args[ self::CONTENT_CLASSES ];
		$this->description       = (string) $args[ self::DESCRIPTION ];

		$this->set_premium_listing_content_visibility( true, false );
	}

	protected function defaults(): array {
		return [
			self::CLASSES           => [],
			self::ATTRS             => [],
			self::CONTAINER_CLASSES => [],
			self::CONTENT_CLASSES   => [],
			self::DESCRIPTION       => '',
		];
	}

	protected function required(): array {
		return [
			self::CLASSES           => [ 'c-block', 'b-provider-alternatives' ],
			self::CONTAINER_CLASSES => [ 'b-provider-alternatives__container', 'l-container', 's-sink', 't-sink' ],
		];
	}

	public function get_attrs(): string {
		if ( is_admin() ) {
			$this->attrs['data-toc-title'] = __( 'Alternatives To', 'tribe' );
		}

		return Markup_Utils::concat_attrs( $this->attrs );
	}

	public function get_classes(): string {
		$classes = [ ...$this->classes ];

		if ( $premium_listing_admin_class = $this->get_premium_listing_content_admin_class() ) {
			$classes[] = $premium_listing_admin_class;
		}

		return Markup_Utils::class_attribute( $classes );
	}

	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	public function get_title(): ?Deferred_Component {
		if ( ! $this->get_provider() ) {
			return null;
		}

		return defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'h2',
			Text_Controller::CLASSES => [
				'b-provider-alternatives__title',
			],
			Text_Controller::CONTENT => 'Alternatives to ' . $this->get_provider()->get_name(),
		] );
	}

	public function get_description(): ?string {
		if ( ! $this->description ) {
			return null;
		}

		return $this->description;
	}

	public function get_overall_score(): ?float {
		$reviews = $this->get_provider()->get_analyst_review();

		if ( empty( $reviews ) ) {
			return null;
		}

		$overall_score = 0;
		$review_count  = 0;

		foreach ( $reviews as $review ) {
			$overall_score += $review->get_score();
			$review_count ++;
		}

		if ( $review_count === 0 || $overall_score === 0 ) {
			return null;
		}

		return (float) $overall_score / $review_count;
	}

	public function get_alternatives(): ?Deferred_Component {
		$content      = '';
		$alternatives = $this->get_provider()->get_alternatives( self::ALTERNATIVES_QTY );

		if ( ! $alternatives || count( $alternatives ) < 2 ) {
			return null;
		}

		foreach ( $alternatives as $alternative ) {
			$name            = $alternative->get_name();
			$score           = $alternative->get_score();
			$logo_url        = $alternative->get_logo_url();
			$redirect_link   = $alternative->get_redirect_link();
			$datalayer_attrs = $alternative->get_visit_link_attrs( self::DATALAYER_BLOCK_NAME, 'cta' );

			$logo = defer_template_part( 'components/image/image', null, [
				Image_Controller::IMG_URL      => $logo_url,
				Image_Controller::AS_BG        => false,
				Image_Controller::LINK_URL     => $redirect_link ?: '',
				Image_Controller::LINK_TARGET  => $redirect_link ? __( '_blank', 'tribe' ) : '',
				Image_Controller::ATTRS        => $datalayer_attrs,
				Image_Controller::SRC_SIZE     => 'medium_large',
				Image_Controller::SRCSET_SIZES => [
					'medium',
					'medium_large',
				],
				Image_Controller::CLASSES      => [
					'b-provider-alternatives__provider_logo',
				],
			] );

			$rating = $score ? defer_template_part( 'components/rating/rating', null, [
				Rating_Controller::SCORE     => ceil( ( (float) $score * 100 ) / 5 ),
				Rating_Controller::SHOW_STAR => false,
				Rating_Controller::MAX_SCORE => 100,
			] ) : '';

			$provider_name = defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'h3',
				Text_Controller::CLASSES => [ 'b-provider-alternatives__provider-name', 'h5' ],
				Text_Controller::CONTENT => $name,
			] );

			$link_name = defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => $redirect_link,
				Link_Controller::CONTENT => $provider_name,
				Link_Controller::TARGET  => $redirect_link ? __( '_blank', 'tribe' ) : '',
				Link_Controller::ATTRS   => $datalayer_attrs,
			] );

			$name_rating_container = defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ 'b-provider-alternatives__provider-name-rating' ],
				Container_Controller::CONTENT => $rating . $link_name,
			] );

			$btn = defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => $redirect_link,
				Link_Controller::CONTENT => __( 'Visit Website', 'tribe' ),
				Link_Controller::TARGET  => $redirect_link ? __( '_blank', 'tribe' ) : '',
				Link_Controller::CLASSES => [ 'a-cta' ],
				Link_Controller::ATTRS   => $datalayer_attrs,
			] );

			$info = defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ 'b-provider-alternatives__item-info' ],
				Container_Controller::CONTENT => $name_rating_container . $btn,
			] );

			$content .= defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ 'b-provider-alternatives__item' ],
				Container_Controller::CONTENT => $logo . $info,
			] );
		}

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ 'b-provider-alternatives__items' ],
			Container_Controller::CONTENT => $content,
		] );
	}

	/**
	 * @return string|null
	 */
	public function get_no_content_error(): ?string {
		return Blocks_Utilities::format_admin_error_message( 'No related content found or there is less than 2 providers to show. The block will not be rendered.' );
	}
}
