<?php declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\listicle\ssl_provider_market_fit;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Admin\Editor\Blocks_Utilities;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Blocks\Types\Listicle\ssl_Provider_Market_Fit\ssl_Provider_Market_Fit;
use Tribe\Project\Templates\Components\Traits\Handles_SSL_Premium_Listing;

class ssl_Provider_Market_Fit_Controller extends Abstract_Controller {
	use Handles_SSL_Premium_Listing;

	public const CLASSES              = 'classes';
	public const ATTRS                = 'attrs';
	public const CONTAINER_CLASSES    = 'container_classes';
	public const CONTENT_CLASSES      = 'content_classes';
	public const TITLE                = 'title';
	public const GOOD_FIT_TITLE       = 'good_fit_title';
	public const GOOD_FIT_DESCRIPTION = 'good_fit_description';
	public const GOOD_FIT_CASES       = 'good_fit_cases';
	public const BAD_FIT_TITLE        = 'bad_fit_title';
	public const BAD_FIT_DESCRIPTION  = 'bad_fit_description';
	public const BAD_FIT_CASES        = 'bad_fit_cases';

	private array  $classes;
	private array  $attrs;
	private array  $container_classes;
	private array  $content_classes;
	private string $title;
	private string $good_fit_title;
	private string $good_fit_description;
	private array  $good_fit_cases;
	private string $bad_fit_title;
	private string $bad_fit_description;
	private array  $bad_fit_cases;

	public function __construct( array $args = [] ) {
		$args                       = $this->parse_args( $args );
		$this->classes              = (array) $args[ self::CLASSES ];
		$this->attrs                = (array) $args[ self::ATTRS ];
		$this->container_classes    = (array) $args[ self::CONTAINER_CLASSES ];
		$this->content_classes      = (array) $args[ self::CONTENT_CLASSES ];
		$this->title                = (string) $args[ self::TITLE ];
		$this->good_fit_title       = (string) $args[ self::GOOD_FIT_TITLE ];
		$this->good_fit_description = (string) $args[ self::GOOD_FIT_DESCRIPTION ];
		$this->good_fit_cases       = (array) $args[ self::GOOD_FIT_CASES ];
		$this->bad_fit_title        = (string) $args[ self::BAD_FIT_TITLE ];
		$this->bad_fit_description  = (string) $args[ self::BAD_FIT_DESCRIPTION ];
		$this->bad_fit_cases        = (array) $args[ self::BAD_FIT_CASES ];

		$this->set_premium_listing_content_visibility( true, true );
	}

	protected function defaults(): array {
		return [
			self::CLASSES              => [],
			self::ATTRS                => [],
			self::CONTAINER_CLASSES    => [],
			self::CONTENT_CLASSES      => [],
			self::TITLE                => '',
			self::GOOD_FIT_TITLE       => '',
			self::GOOD_FIT_DESCRIPTION => '',
			self::GOOD_FIT_CASES       => [],
			self::BAD_FIT_TITLE        => '',
			self::BAD_FIT_DESCRIPTION  => '',
			self::BAD_FIT_CASES        => [],
		];
	}

	protected function required(): array {
		return [
			self::CLASSES           => [ 'c-block', 'b-provider-market-fit' ],
			self::CONTAINER_CLASSES => [
				'b-provider-market-fit__container',
				'l-container',
				't-sink',
				's-sink',
			],
		];
	}

	public function get_attrs(): string {
		if ( is_admin() ) {
			$this->attrs['data-toc-title'] = __( 'Use Cases', 'tribe' );
		}

		return Markup_Utils::concat_attrs( $this->attrs );
	}

	public function get_classes(): string {
		$classes = [ ...$this->classes ];

		if ( $premium_listing_admin_class = $this->get_premium_listing_content_admin_class() ) {
			$classes[] = $premium_listing_admin_class;
		}

		return Markup_Utils::class_attribute( $classes );
	}

	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	public function get_title(): string {
		if ( empty( $this->title ) ) {
			return '';
		}

		return $this->title;
	}

	public function get_good_fit_title(): ?Deferred_Component {
		if ( empty( $this->good_fit_title ) ) {
			return null;
		}

		return defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'h4',
			Text_Controller::CONTENT => $this->good_fit_title,
		] );
	}

	public function get_good_fit_description(): string {
		if ( empty( $this->good_fit_description ) ) {
			return '';
		}

		return $this->good_fit_description;
	}

	public function get_good_fit_cases(): array {
		return $this->good_fit_cases;
	}

	public function get_bad_fit_cases(): array {
		return $this->bad_fit_cases;
	}

	public function get_cases_repeater( $cases, $bad_cases = false ): ?Deferred_Component {
		if ( ! $cases ) {
			return null;
		}

		if ( ! $bad_cases ) {
			$list_class = 'b-provider-market-fit__list--good-fit';
		} else {
			$list_class = 'b-provider-market-fit__list--bad-fit';
		}

		$cases_container = '';

		foreach ( $cases as $case ) {
			$title = defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'h5',
				Text_Controller::CLASSES => [ 'b-provider-market-fit__list__item__title' ],
				Text_Controller::CONTENT => $case[ ssl_Provider_Market_Fit::CASE_TITLE ],
			] );

			$description = defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ 'b-provider-market-fit__list__item__description' ],
				Container_Controller::CONTENT => $case[ ssl_Provider_Market_Fit::CASE_DESCRIPTION ],
			] );

			$cases_container .= defer_template_part( 'components/container/container', null, [
				Container_Controller::TAG     => 'li',
				Container_Controller::CLASSES => [ 'b-provider-market-fit__list__item' ],
				Container_Controller::CONTENT => $title . $description,
			] );
		}

		$list = defer_template_part( 'components/container/container', null, [
			Container_Controller::TAG     => 'ul',
			Container_Controller::CLASSES => [ 'b-provider-market-fit__list', $list_class ],
			Container_Controller::CONTENT => $cases_container,
		] );

		return $list;
	}

	public function get_bad_fit_title(): ?Deferred_Component {
		if ( empty( $this->bad_fit_title ) ) {
			return null;
		}

		return defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'h4',
			Text_Controller::CONTENT => $this->bad_fit_title,
		] );
	}

	public function get_bad_fit_description(): string {
		if ( empty( $this->bad_fit_description ) ) {
			return '';
		}

		return $this->bad_fit_description;
	}

	public function get_content(): string {
		return $this->get_good_fit_title() .
			$this->get_good_fit_description() .
			$this->get_cases_repeater( $this->get_good_fit_cases() ) .
			$this->get_bad_fit_title() .
			$this->get_bad_fit_description() .
			$this->get_cases_repeater( $this->get_bad_fit_cases(), true );
	}

	public function get_no_content_error(): ?string {
		return Blocks_Utilities::format_admin_error_message( 'Add some content to preview the block.' );
	}
}
