<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\listicle\ssl_xvsy_similarities;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Admin\Editor\Blocks_Utilities;
use Tribe\Project\Templates\Components\ssl_XvsY_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;

class ssl_XvsY_Similarities_Block_Controller extends ssl_XvsY_Controller {
	public const CONTAINER_CLASSES    = 'container_classes';
	public const CLASSES              = 'classes';
	public const ATTRS                = 'attrs';
	public const ROOT_CLASS           = 'b-ssl-xvsy-similarities';
	public const INTRODUCTION_TEXT    = 'intro_text';
	public const DATALAYER_BLOCK_NAME = 'xvsy-similarities';

	private array  $container_classes;
	private array  $classes;
	private array  $attrs;
	private string $introduction_text;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		parent::__construct( $args );

		$args = $this->parse_args( $args );

		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->introduction_text = (string) $args[ self::INTRODUCTION_TEXT ];
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [],
			self::ATTRS             => [],
		];
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [ 'c-block', self::ROOT_CLASS ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		if ( is_admin() ) {
			$this->attrs['data-toc-title'] = __( 'Similarities', 'tribe' );
		}

		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	/**
	 * @return array|null
	 */
	private function get_formatted_similarities(): ?array {
		$similarities = $this->get_similarities();

		if ( ! $similarities ) {
			return null;
		}

		$similarities_categories = [];

		foreach ( $similarities as $similarity ) {
			if ( empty( $similarity['description'] ) || empty( $similarity['comparison_attribute'] ) ) {
				continue;
			}

			$category_key                             = $similarity['comparison_attribute'];
			$similarities_categories[ $category_key ] = $similarity['description'];
		}

		ksort( $similarities_categories );

		return $similarities_categories;
	}

	/**
	 * @return string|null
	 */
	public function get_accordion_content(): ?string {
		$similarities = $this->get_formatted_similarities();

		if ( ! $similarities ) {
			return null;
		}

		$introduction_text = '';

		if ( $intro = $this->introduction_text ) {
			$introduction_text = $intro;
		}

		$tbody_rows  = '';
		$buttons     = '';
		$buttons_row = '';

		foreach ( [ $this->get_provider_a(), $this->get_provider_b() ] as $index => $provider ) {
			if ( $provider->get_redirect_link() ) {
				$buttons .= defer_template_part( 'components/link/link', null, [
					Link_Controller::URL     => esc_url( $provider->get_redirect_link() ),
					Link_Controller::CONTENT => __( 'Visit', 'tribe' ) . ' ' . esc_html( $provider->get_name() ),
					Link_Controller::TARGET  => '_blank',
					Link_Controller::CLASSES => [ 'a-btn' ],
					Link_Controller::ATTRS   => 0 === $index ? $this->get_provider_a_visit_link_attrs( self::DATALAYER_BLOCK_NAME, 'cta' ) : $this->get_provider_b_visit_link_attrs( self::DATALAYER_BLOCK_NAME, 'cta' ),
				] );
			}
		}

		$buttons_row .= defer_template_part( 'components/container/container', null, [
			Container_Controller::TAG     => 'td',
			Container_Controller::CONTENT => $buttons,
			Container_Controller::ATTRS   => [ 'colspan' => '2' ],
		] );

		$provider_buttons_row = defer_template_part( 'components/container/container', null, [
			Container_Controller::TAG     => 'tr',
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__table__buttons' ],
			Container_Controller::CONTENT => $buttons_row,
		] );

		$category_index = 0;
		$mid_index      = ceil( count( $similarities ) / 2 );

		foreach ( $similarities as $category => $similarity ) {
			$similarity_columns = defer_template_part( 'components/container/container', null, [
				Container_Controller::TAG     => 'th',
				Container_Controller::CONTENT => esc_html( strip_tags( $category ) ),
			] );

			$similarity_columns .= defer_template_part( 'components/container/container', null, [
				Container_Controller::TAG     => 'td',
				Container_Controller::CONTENT => esc_html( strip_tags( $similarity ) ),
			] );

			$tbody_rows .= defer_template_part( 'components/container/container', null, [
				Container_Controller::TAG     => 'tr',
				Container_Controller::CONTENT => $similarity_columns,
			] );

			if ( $category_index == $mid_index && $category_index < count( $similarities ) - 2 ) {
				$tbody_rows .= $provider_buttons_row;
			}

			$category_index ++;
		}

		$table = defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__table' ],
			Container_Controller::TAG     => 'table',
			Container_Controller::CONTENT => "<tbody>$tbody_rows</tbody><tfoot>$provider_buttons_row</tfoot>",
		] );

		return $introduction_text . $table;
	}

	/**
	 * @return string
	 */
	public function get_accordion_title(): string {
		return 'Similarities Between ' . $this->get_provider_a()->get_name() . ' And ' . $this->get_provider_b()->get_name();
	}

	/**
	 * @return null|string
	 */
	public function get_no_content_error(): ?string {
		return Blocks_Utilities::format_admin_error_message( 'No API Data found. Make sure that the the data exists and have the correct attributes.' );
	}
}
