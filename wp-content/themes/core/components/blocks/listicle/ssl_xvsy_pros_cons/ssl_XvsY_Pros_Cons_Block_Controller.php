<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\listicle\ssl_xvsy_pros_cons;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Object_Meta\PPL_Options_Meta;
use Tribe\Project\Object_Meta\PPL_Settings;
use Tribe\Project\Templates\Components\ssl_XvsY_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Pros_Cons\ssl_XvsY_Pros_Cons;
use Tribe\Project\Templates\Models\ssl_Pub_Review;

class ssl_XvsY_Pros_Cons_Block_Controller extends ssl_XvsY_Controller {
	public const CONTAINER_CLASSES    = 'container_classes';
	public const CLASSES              = 'classes';
	public const ATTRS                = 'attrs';
	public const ROOT_CLASS           = 'b-ssl-xvsy-pros-cons';
	public const INTRODUCTION_TEXT    = 'intro_text';
	public const PRIMARY_CTA          = 'primary_cta';
	public const DATALAYER_BLOCK_NAME = 'xvsy-pros-cons';

	private array  $container_classes;
	private array  $classes;
	private array  $attrs;
	private string $introduction_text;
	private string $primary_cta;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		parent::__construct( $args );

		$args = $this->parse_args( $args );

		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->introduction_text = (string) $args[ self::INTRODUCTION_TEXT ];
		$this->primary_cta       = (string) $args[ self::PRIMARY_CTA ];
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [],
			self::ATTRS             => [],
			self::PRIMARY_CTA       => '',
		];
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [ 'c-block', self::ROOT_CLASS ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		if ( is_admin() ) {
			$this->attrs['data-toc-title'] = __( 'Best Use Cases', 'tribe' );
		}

		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	/**
	 * @param string $title
	 * @param array  $items
	 *
	 * @return string|null
	 */
	private function get_pros_cons_list( array $items, string $class ): Deferred_Component {
		$list_items = '';

		foreach ( $items as $item ) {
			$list_items .= defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'li',
				Text_Controller::CONTENT => esc_html( $item ),
			] );
		}

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::TAG     => 'ul',
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__' . $class ],
			Container_Controller::CONTENT => $list_items,
		] );
	}

	/**
	 * @param string $provider_letter
	 *
	 * @return string|null
	 */
	private function get_provider_pros_cons( string $provider_letter ): ?string {
		$list_content = '';

		$pub_review = $provider_letter === 'a' ? $this->get_provider_a_pub_review() : $this->get_provider_b_pub_review();

		if ( ! $pub_review ) {
			return null;
		}

		if ( $pros = $pub_review->get_pros_cons( ssl_Pub_Review::PROS ) ) {
			$content = defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'span',
				Text_Controller::CLASSES => [ 't-overline' ],
				Text_Controller::CONTENT => __( 'Pros', 'tribe' ),
			] );

			$list_content .= defer_template_part( 'components/container/container', null, [
				Container_Controller::CONTENT => $content . $this->get_pros_cons_list( $pros, 'pros' ),
			] );
		}

		if ( $cons = $pub_review->get_pros_cons( ssl_Pub_Review::CONS ) ) {
			$content = defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'span',
				Text_Controller::CLASSES => [ 't-overline' ],
				Text_Controller::CONTENT => __( 'Cons', 'tribe' ),
			] );

			$list_content .= defer_template_part( 'components/container/container', null, [
				Container_Controller::CONTENT => $content . $this->get_pros_cons_list( $cons, 'cons' ),
			] );
		}

		return $list_content;
	}

	/**
	 * @return string
	 */
	public function get_accordion_content(): string {
		$content = '';

		if ( $intro = $this->introduction_text ) {
			$content .= $intro;
		}

		$comparison_content = '';

		foreach ( [ $this->get_provider_a(), $this->get_provider_b() ] as $index => $provider ) {
			$comparison_content .= defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'h4',
				Text_Controller::CONTENT => esc_html( $provider->get_name() ),
			] );

			$comparison_content .= $this->get_provider_pros_cons( $index === 0 ? 'a' : 'b' );

			$comparison_content .= defer_template_part( 'components/container/container', null, [
				Container_Controller::TAG     => 'div',
				Container_Controller::CONTENT => defer_template_part( 'components/link/link', null, [
					Link_Controller::URL     => esc_url( $index === 0 ? $this->get_provider_a()->get_redirect_link() : $this->get_provider_b()->get_redirect_link() ),
					Link_Controller::CONTENT => 'Visit ' . esc_html( $provider->get_name() ),
					Link_Controller::TARGET  => '_blank',
					Link_Controller::CLASSES => [ 'a-btn' ],
					Link_Controller::ATTRS   => $index === 0 ? $this->get_provider_a_visit_link_attrs( self::DATALAYER_BLOCK_NAME, 'cta' ) : $this->get_provider_b_visit_link_attrs( self::DATALAYER_BLOCK_NAME, 'cta' ),
				] ),
			] );
		}

		$content .= defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__comparison' ],
			Container_Controller::CONTENT => $comparison_content,
		] );

		return $content;
	}

	/**
	 * @return string
	 */
	public function get_accordion_title(): string {
		return sprintf( __( '%s vs %s: Pros & Cons', 'tribe' ), $this->get_provider_a()->get_name(), $this->get_provider_b()->get_name() );
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function get_primary_cta(): ?Deferred_Component {
		$primary_cta = $this->primary_cta;
		$taxonomy_id = null;
		$classes     = [];

		$tool_name_a = sanitize_title( $this->get_provider_a()->get_name() );
		$tool_name_b = sanitize_title( $this->get_provider_b()->get_name() );

		if ( ! empty( $ppl_option_category_id = (string) get_field( PPL_Options_Meta::CATEGORY_ID, get_the_ID() ) ) ) {
			$taxonomy_id = $ppl_option_category_id;
		} else {
			$taxonomy_id = (string) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_CATEGORY_ID_DEFAULT, 'option' );
		}

		if ( empty( $taxonomy_id ) ) {
			return null;
		}

		$classes = [
			'item-single__cta-link',
			'a-btn',
		];

		$ppl_url = '#modal-id-ppl-form||category-id-' . $taxonomy_id . '||post-id-' . get_the_ID();

		if ( $primary_cta === ssl_XvsY_Pros_Cons::CTA_OPTION_GET_CUSTOM_QUOTE_UTM ) {
			$ppl_url .= '||utm-provider-' . $tool_name_a . '--provider-' . $tool_name_b;

			return defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => $ppl_url,
				Link_Controller::CONTENT => __( 'Get Custom Quote', 'tribe' ),
				Link_Controller::TARGET  => '_blank',
				Link_Controller::CLASSES => $classes,
			] );
		} elseif ( $primary_cta === ssl_XvsY_Pros_Cons::CTA_OPTION_GET_EXPERT_ADVICE_NO_UTM ) {
			return defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => $ppl_url,
				Link_Controller::CONTENT => __( 'Get Expert Advice', 'tribe' ),
				Link_Controller::TARGET  => '_blank',
				Link_Controller::CLASSES => $classes,
			] );

		} elseif ( $primary_cta === ssl_XvsY_Pros_Cons::CTA_OPTION_BOOK_DEMO_UTM ) {
			$ppl_url .= '||utm-provider-' . $tool_name_a . '--provider-' . $tool_name_b;

			return defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => $ppl_url,
				Link_Controller::CONTENT => __( 'Book Demo', 'tribe' ),
				Link_Controller::TARGET  => '_blank',
				Link_Controller::CLASSES => $classes,
			] );

		} else {
			return null;
		}
	}

}
