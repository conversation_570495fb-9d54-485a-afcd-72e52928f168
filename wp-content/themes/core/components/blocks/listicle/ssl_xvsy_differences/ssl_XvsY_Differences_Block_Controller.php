<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\listicle\ssl_xvsy_differences;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Admin\Editor\Blocks_Utilities;
use Tribe\Project\Templates\Components\ssl_XvsY_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;

class ssl_XvsY_Differences_Block_Controller extends ssl_XvsY_Controller {
	public const CONTAINER_CLASSES    = 'container_classes';
	public const CLASSES              = 'classes';
	public const ATTRS                = 'attrs';
	public const ROOT_CLASS           = 'b-ssl-xvsy-differences';
	public const INTRODUCTION_TEXT    = 'intro_text';
	public const DATALAYER_BLOCK_NAME = 'xvsy-differences';

	private array  $container_classes;
	private array  $classes;
	private array  $attrs;
	private string $introduction_text;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		parent::__construct( $args );

		$args = $this->parse_args( $args );

		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->introduction_text = (string) $args[ self::INTRODUCTION_TEXT ];
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [],
			self::ATTRS             => [],
		];
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [ 'c-block', self::ROOT_CLASS ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		if ( is_admin() ) {
			$this->attrs['data-toc-title'] = __( 'Differences', 'tribe' );
		}

		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	/**
	 * @return array|null
	 */
	private function get_formatted_differences(): ?array {
		$differences = $this->get_differences();

		if ( ! $differences ) {
			return null;
		}

		$differences_categories = [];
		$provider_ids           = [ $this->get_provider_a()->get_id(), $this->get_provider_b()->get_id() ];

		foreach ( $differences as $difference ) {
			if ( empty( $difference['provider_id'] ) || ! in_array( $difference['provider_id'], $provider_ids ) || empty( $difference['description'] ) || empty( $difference['comparison_attribute'] ) ) {
				continue;
			}

			$category_key   = $difference['comparison_attribute'];
			$provider_index = $difference['provider_id'] == $this->get_provider_a()->get_id() ? 0 : 1;

			if ( ! isset( $differences_categories[ $category_key ] ) ) {
				$differences_categories[ $category_key ] = [ '', '' ];
			}

			$differences_categories[ $category_key ][ $provider_index ] = $difference['description'];
		}

		ksort( $differences_categories );

		return $differences_categories;
	}

	/**
	 * @return string|null
	 */
	public function get_accordion_content(): ?string {
		$differences = $this->get_formatted_differences();

		if ( ! $differences ) {
			return null;
		}

		$introduction_text = '';

		if ( $intro = $this->introduction_text ) {
			$introduction_text = $intro;
		}

		$thead_columns   = '<th></th>';
		$buttons_columns = '<td></td>';
		$tbody_rows      = '';

		foreach ( [ $this->get_provider_a(), $this->get_provider_b() ] as $index => $provider ) {
			$thead_columns .= defer_template_part( 'components/container/container', null, [
				Container_Controller::TAG     => 'th',
				Container_Controller::CONTENT => $provider->get_name(),
			] );

			if ( $provider->get_redirect_link() ) {
				$buttons_columns .= defer_template_part( 'components/container/container', null, [
					Container_Controller::TAG     => 'td',
					Container_Controller::CONTENT => defer_template_part( 'components/link/link', null, [
						Link_Controller::URL     => esc_url( $provider->get_redirect_link() ),
						Link_Controller::CONTENT => __( 'Visit', 'tribe' ) . ' ' . esc_html( $provider->get_name() ),
						Link_Controller::TARGET  => '_blank',
						Link_Controller::CLASSES => [ 'a-btn' ],
						Link_Controller::ATTRS   => 0 === $index ? $this->get_provider_a_visit_link_attrs( self::DATALAYER_BLOCK_NAME, 'cta' ) : $this->get_provider_b_visit_link_attrs( self::DATALAYER_BLOCK_NAME, 'cta' ),
					] ),
				] );
			}
		}

		$provider_buttons_row = defer_template_part( 'components/container/container', null, [
			Container_Controller::TAG     => 'tr',
			Container_Controller::CONTENT => $buttons_columns,
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__table__buttons' ],
		] );

		$thead_row = defer_template_part( 'components/container/container', null, [
			Container_Controller::TAG     => 'tr',
			Container_Controller::CONTENT => $thead_columns,
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__table__buttons' ],
		] );

		$category_index = 0;
		$mid_index      = ceil( count( $differences ) / 2 );

		foreach ( $differences as $category => $difference ) {
			$diff_columns = defer_template_part( 'components/container/container', null, [
				Container_Controller::TAG     => 'th',
				Container_Controller::CONTENT => esc_html( strip_tags( $category ) ),
			] );

			foreach ( $difference as $column_index => $description ) {
				$diff_columns .= defer_template_part( 'components/container/container', null, [
					Container_Controller::TAG     => 'td',
					Container_Controller::CONTENT => esc_html( strip_tags( $description ) ),
					Container_Controller::ATTRS   => [ 'data-provider-name' => ( 0 === $column_index ? $this->get_provider_a()->get_name() : $this->get_provider_b()->get_name() ) ],
				] );
			}

			$tbody_rows .= defer_template_part( 'components/container/container', null, [
				Container_Controller::TAG     => 'tr',
				Container_Controller::CONTENT => $diff_columns,
			] );

			if ( $category_index == $mid_index && $category_index < count( $differences ) - 2 ) {
				$tbody_rows .= $provider_buttons_row;
			}

			$category_index ++;
		}

		$table = defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__table' ],
			Container_Controller::TAG     => 'table',
			Container_Controller::CONTENT => "<thead>$thead_row</thead><tbody>$tbody_rows</tbody><tfoot>$provider_buttons_row</tfoot>",
		] );

		return $introduction_text . $table;
	}

	/**
	 * @return string
	 */
	public function get_accordion_title(): string {
		return 'Differences Between ' . $this->get_provider_a()->get_name() . ' And ' . $this->get_provider_b()->get_name();
	}

	/**
	 * @return null|string
	 */
	public function get_no_content_error(): ?string {
		return Blocks_Utilities::format_admin_error_message( 'No API Data found. Make sure that the the data exists and have the correct attributes.' );
	}
}
