<?php declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\listicle\ssl_provider_faqs;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Templates\Components\ssl_Provider_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Models\Accordion_Row;

class ssl_Provider_Faqs_Controller extends ssl_Provider_Controller {
	public const ROWS              = 'rows';
	public const CLASSES           = 'classes';
	public const ATTRS             = 'attrs';
	public const CONTAINER_CLASSES = 'container_classes';
	public const CONTENT_CLASSES   = 'content_classes';
	public const DESCRIPTION       = 'description';

	private array  $classes;
	private array  $attrs;
	private array  $container_classes;
	private array  $content_classes;
	private string $description;

	public function __construct( array $args = [] ) {
		parent::__construct( $args );

		$args                    = $this->parse_args( $args );
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->content_classes   = (array) $args[ self::CONTENT_CLASSES ];
		$this->description       = (string) $args[ self::DESCRIPTION ];
	}

	protected function defaults(): array {
		return [
			self::CLASSES           => [],
			self::ATTRS             => [],
			self::CONTAINER_CLASSES => [],
			self::CONTENT_CLASSES   => [],
			self::DESCRIPTION       => '',
		];
	}

	protected function required(): array {
		return [
			self::CLASSES           => [ 'c-block', 'b-provider-faqs' ],
			self::CONTAINER_CLASSES => [
				'l-container',
				's-sink',
				't-sink',
			],
		];
	}

	public function get_attrs(): string {
		if ( is_admin() ) {
			$this->attrs['data-toc-title'] = __( 'FAQs', 'tribe' );
		}

		return Markup_Utils::concat_attrs( $this->attrs );
	}

	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	public function get_title(): ?Deferred_Component {
		if ( ! $this->get_provider()->get_name() ) {
			return null;
		}

		return defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'h2',
			Text_Controller::CLASSES => [ 'b-provider-faqs__title' ],
			Text_Controller::CONTENT => $this->get_provider()->get_name() . ' FAQs',
		] );
	}

	public function get_description(): ?string {
		if ( empty( $this->description ) ) {
			return null;
		}

		return $this->description;
	}

	public function get_faqs_args(): array {
		$data = [];

		foreach ( $this->get_provider()->get_faqs() as $faq_item ) {
			$data[] = new Accordion_Row(
				$faq_item['question'],
				$faq_item['answer']
			);
		}

		return [
			self::ROWS => $data,
		];
	}

}
