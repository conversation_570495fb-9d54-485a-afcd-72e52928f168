<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\media_text;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Blocks\Types\Media_Text\Media_Text as Media_Text_Block;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\content_block\Content_Block_Controller;
use Tribe\Project\Templates\Components\blocks\links\Links_Block_Controller;
use Tribe\Project\Blocks\Types\Links\Links;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\Traits\With_Lottie_Data;
use Tribe\Project\Theme\Config\Image_Sizes;

class Media_Text_Block_Controller extends Abstract_Controller {
	use With_Lottie_Data;

	public const CLASSES                      = 'classes';
	public const ATTRS                        = 'attrs';
	public const WIDTH                        = 'width';
	public const LAYOUT                       = 'layout';
	public const MEDIA_IMAGE_SIZE             = 'media_image_size';
	public const CONTAINER_CLASSES            = 'container_classes';
	public const MEDIA_CLASSES                = 'media_classes';
	public const CONTENT_CLASSES              = 'content_classes';
	public const MEDIA_TYPE                   = 'media_type';
	public const IMAGE                        = 'image';
	public const VIDEO                        = 'video';
	public const HTML                         = 'html';
	public const TITLE                        = 'title';
	public const DESCRIPTION                  = 'description';
	public const LIST                         = 'list';
	public const LIST_ITEM                    = 'list_item';
	public const FOOTER_CONTENT               = 'footer_content';
	public const CTA                          = 'cta';
	public const CTA_ID                       = 'cta_id';
	public const SECONDARY_CTA                = 'secondary_cta';
	public const SECONDARY_CTA_ID             = 'secondary_cta_id';
	public const BUTTONS                      = 'buttons';
	public const HEADING_TAG                  = 'heading_tag';
	public const HEADING_TAG_H1               = 'heading_tag_h1';
	public const MEDIA_STACK_INVERSE          = 'media_stack_inverse';
	public const CUSTOM_CLASSES_PRIMARY_CTA   = 'custom_classes_primary_cta';
	public const CUSTOM_CLASSES_SECONDARY_CTA = 'custom_classes_secondary_cta';

	/**
	 * @var int|string
	 */
	private $image;

	private array  $classes;
	private array  $attrs;
	private string $width;
	private string $layout;
	private string $media_image_size;
	private array  $container_classes;
	private array  $media_classes;
	private array  $content_classes;
	private string $media_type;
	private string $video;
	private string $html;
	private string $title;
	private string $description;
	private array  $cta;
	private string $cta_id;
	private string $secondary_cta_id;
	private array  $secondary_cta;
	private array  $buttons;
	private string $heading_tag;
	private string $heading_tag_h1;
	private bool   $media_stack_inverse;
	private string $custom_classes_primary_cta;
	private string $custom_classes_secondary_cta;
	private array  $list_items;
	private string $footer_content;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		$args = $this->parse_args( $args );

		$this->classes                      = (array) $args[ self::CLASSES ];
		$this->attrs                        = (array) $args[ self::ATTRS ];
		$this->width                        = (string) $args[ self::WIDTH ];
		$this->layout                       = (string) $args[ self::LAYOUT ];
		$this->media_image_size             = (string) $args[ self::MEDIA_IMAGE_SIZE ];
		$this->container_classes            = (array) $args[ self::CONTAINER_CLASSES ];
		$this->media_classes                = (array) $args[ self::MEDIA_CLASSES ];
		$this->content_classes              = (array) $args[ self::CONTENT_CLASSES ];
		$this->media_type                   = (string) $args[ self::MEDIA_TYPE ];
		$this->image                        = $args[ self::IMAGE ];
		$this->video                        = (string) $args[ self::VIDEO ];
		$this->html                         = (string) $args[ self::HTML ];
		$this->title                        = (string) $args[ self::TITLE ];
		$this->description                  = (string) $args[ self::DESCRIPTION ];
		$this->list_items                   = (array) $args[ self::LIST ];
		$this->cta                          = (array) $args[ self::CTA ];
		$this->cta_id                       = (string) $args[ self::CTA_ID ];
		$this->secondary_cta                = (array) $args[ self::SECONDARY_CTA ];
		$this->secondary_cta_id             = (string) $args[ self::SECONDARY_CTA_ID ];
		$this->buttons                      = (array) $args[ self::BUTTONS ];
		$this->heading_tag                  = (string) $args[ self::HEADING_TAG ];
		$this->heading_tag_h1               = (string) $args[ self::HEADING_TAG_H1 ];
		$this->media_stack_inverse          = (bool) $args[ self::MEDIA_STACK_INVERSE ];
		$this->custom_classes_primary_cta   = (string) $args[ self::CUSTOM_CLASSES_PRIMARY_CTA ];
		$this->custom_classes_secondary_cta = (string) $args[ self::CUSTOM_CLASSES_SECONDARY_CTA ];
		$this->footer_content               = (string) $args[ self::FOOTER_CONTENT ];

		$this->fill_lottie_data( $args );
	}

	protected function defaults(): array {
		return [
			self::CLASSES                      => [],
			self::ATTRS                        => [],
			self::WIDTH                        => Media_Text_Block::WIDTH_GRID,
			self::LAYOUT                       => Media_Text_Block::MEDIA_LEFT,
			self::MEDIA_IMAGE_SIZE             => Media_Text_Block::MEDIA_IMAGE_SIZE_REGULAR,
			self::CONTAINER_CLASSES            => [],
			self::MEDIA_CLASSES                => [],
			self::CONTENT_CLASSES              => [],
			self::MEDIA_TYPE                   => Media_Text_Block::IMAGE,
			self::IMAGE                        => null,
			self::VIDEO                        => '',
			self::HTML                         => '',
			self::LOTTIE_DATA                  => [],
			self::LOTTIE_JSON_URL              => '',
			self::TITLE                        => '',
			self::DESCRIPTION                  => '',
			self::LIST                         => [],
			self::CTA                          => [],
			self::CTA_ID                       => '',
			self::SECONDARY_CTA                => [],
			self::SECONDARY_CTA_ID             => '',
			self::BUTTONS                      => [],
			self::HEADING_TAG                  => '',
			self::HEADING_TAG_H1               => '',
			self::MEDIA_STACK_INVERSE          => false,
			self::CUSTOM_CLASSES_PRIMARY_CTA   => '',
			self::CUSTOM_CLASSES_SECONDARY_CTA => '',
			self::FOOTER_CONTENT               => '',
		];
	}

	protected function required(): array {
		return [
			self::CLASSES           => [ 'c-block', 'b-media-text' ],
			self::CONTAINER_CLASSES => [ 'b-media-text__container' ],
			self::MEDIA_CLASSES     => [ 'b-media-text__media' ],
			self::CONTENT_CLASSES   => [ 'b-media-text__content' ],
		];
	}

	public function get_classes(): string {
		$this->classes[] = 'c-block--layout-' . $this->layout;

		if ( $this->media_stack_inverse ) {
			$this->classes[] = 'c-block--layout-center--inversed';
		}

		$this->classes[] = 'c-block--width-' . $this->width;

		if ( $this->width === Media_Text_Block::WIDTH_CONTENT ) {
			$this->classes[] = 'c-block--width-' . Media_Text_Block::WIDTH_GRID;
		}

		if ( $this->media_type === Media_Text_Block::IMAGE ) {
			$this->classes[] = 'c-block--media-size-' . $this->media_image_size;
		}

		return Markup_Utils::class_attribute( $this->classes );
	}

	public function get_attrs(): string {
		return Markup_Utils::concat_attrs( $this->attrs );
	}

	public function get_container_classes(): string {
		if ( $this->width === Media_Text_Block::WIDTH_GRID || $this->width === Media_Text_Block::WIDTH_CONTENT ) {
			$this->container_classes[] = 'l-container';
		}

		return Markup_Utils::class_attribute( $this->container_classes );
	}

	public function get_content_classes(): string {
		if ( $this->width === Media_Text_Block::WIDTH_FULL ) {
			$this->content_classes[] = 'l-container';
		}

		if ( $this->heading_tag === $this->heading_tag_h1 ) {
			$this->content_classes[] = 'b-media-text--includes-h1';
		}

		return Markup_Utils::class_attribute( $this->content_classes );
	}

	public function get_custom_primary_cta_classes(): array {
		if ( empty( $this->custom_classes_primary_cta ) ) {
			return [];
		}

		$custom_classes = explode( ' ', $this->custom_classes_primary_cta );

		return $custom_classes;
	}

	public function get_custom_secondary_cta_classes(): array {
		if ( empty( $this->custom_classes_secondary_cta ) ) {
			return [];
		}

		$custom_classes = explode( ' ', $this->custom_classes_secondary_cta );

		return $custom_classes;
	}

	public function get_media_classes(): string {
		if ( $this->get_media_type() ) {
			$this->media_classes[] = 'b-media-text__media--' . $this->get_media_type();
		}

		return Markup_Utils::class_attribute( $this->media_classes );
	}

	public function get_media_type(): string {
		return $this->media_type;
	}

	/**
	 * @return array
	 */
	public function get_content_args(): array {
		if ( ! empty( $this->get_buttons() ) ) {
			return [
				Content_Block_Controller::TITLE   => $this->get_title(),
				Content_Block_Controller::CONTENT => $this->get_content(),
				Content_Block_Controller::LAYOUT  => $this->layout === Media_Text_Block::MEDIA_CENTER ? Content_Block_Controller::LAYOUT_INLINE : Content_Block_Controller::LAYOUT_LEFT,
				Content_Block_Controller::CLASSES => [
					'c-block__content-block',
					'c-block__header',
					'b-media-text__content-container',
				],
			];
		}

		return [
			Content_Block_Controller::TITLE         => $this->get_title(),
			Content_Block_Controller::CONTENT       => $this->get_content(),
			Content_Block_Controller::CTA           => $this->get_cta(),
			Content_Block_Controller::SECONDARY_CTA => $this->get_secondary_cta(),
			Content_Block_Controller::LAYOUT        => $this->layout === Media_Text_Block::MEDIA_CENTER ? Content_Block_Controller::LAYOUT_INLINE : Content_Block_Controller::LAYOUT_LEFT,
			Content_Block_Controller::CLASSES       => [
				'c-block__content-block',
				'c-block__header',
				'b-media-text__content-container',
			],
		];
	}

	/**
	 * @return Deferred_Component
	 */
	private function get_title(): Deferred_Component {
		return defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => $this->heading_tag,
			Text_Controller::CLASSES => [
				'c-block__title',
				'b-media-text__title',
				'h3',
			],
			Text_Controller::CONTENT => $this->title ?? '',
		] );
	}

	/**
	 * @return Deferred_Component
	 */
	private function get_content(): Deferred_Component {
		$content = $this->description ?? '';

		if ( ! empty( $this->get_stacked_buttons() ) ) {
			$content .= $this->get_stacked_buttons();
		}

		if ( in_array( $this->layout, [
				Media_Text_Block::MEDIA_LEFT_LIST,
				Media_Text_Block::MEDIA_RIGHT_LIST,
			] ) && $this->list_items ) {
			$content .= $this->get_list();
		}

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [
				'c-block__description',
				'b-media-text__text',
				't-sink',
				's-sink',
			],
			Container_Controller::CONTENT => $content,
		] );
	}

	private function get_list(): ?Deferred_Component {
		if ( empty( $this->list_items ) ) {
			return null;
		}

		return defer_template_part( 'components/blocks/links/links', null, [
			Links_Block_Controller::COLUMN_1 => [
				Links::LIST_TYPE => Links::OPTION_CHECKLIST,
				Links::LINKS     => array_map( function ( $list_item ) {
					return [
						Links::LINK_ICON    => '',
						Links::LIST_CONTENT => $list_item,
						Links::LINK_TOGGLE  => true,
					];
				}, $this->list_items ),
			],
		] );
	}

	private function get_stacked_buttons(): string {
		if ( empty( $this->get_buttons() ) ) {
			return '';
		}

		$buttons = '';
		$result  = '';

		foreach ( $this->get_buttons() as $button ) {
			$buttons .= defer_template_part( 'components/link/link', null, $button );
		}

		$result .= defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ 'b-media-text__buttons-list' ],
			Container_Controller::CONTENT => $buttons,
		] );

		return $result;
	}

	/**
	 * @return Deferred_Component
	 */
	private function get_cta(): Deferred_Component {
		$cta = wp_parse_args( $this->cta, [
			'content' => '',
			'url'     => '',
			'target'  => '',
		] );

		$cta_id = 'media-text-cta-' . sanitize_title( $cta['content'] );

		if ( ! empty( $this->cta_id ) ) {
			$cta_id = sanitize_title( $this->cta_id );
		}

		return defer_template_part( 'components/link/link', null, [
			Link_Controller::URL     => $cta['url'],
			Link_Controller::CONTENT => $cta['content'] ?: $cta['url'],
			Link_Controller::TARGET  => $cta['target'],
			Link_Controller::ID      => $cta_id,
			Link_Controller::CLASSES => [
				'c-block__cta-link',
				'a-btn',
				...$this->get_custom_primary_cta_classes() ?? '',
			],
		] );
	}

	/**
	 * @return Deferred_Component
	 */
	private function get_secondary_cta(): Deferred_Component {
		$secondary_cta = wp_parse_args( $this->secondary_cta, [
			'content' => '',
			'url'     => '',
			'target'  => '',
		] );

		$secondary_cta_id = 'media-text-secondary-cta-' . sanitize_title( $secondary_cta['content'] );

		if ( ! empty( $this->secondary_cta_id ) ) {
			$secondary_cta_id = sanitize_title( $this->secondary_cta_id );
		}

		return defer_template_part( 'components/link/link', null, [
			Link_Controller::URL     => $secondary_cta['url'],
			Link_Controller::CONTENT => $secondary_cta['content'] ?: $secondary_cta['url'],
			Link_Controller::TARGET  => $secondary_cta['target'],
			Link_Controller::ID      => $secondary_cta_id,
			Link_Controller::CLASSES => [
				'c-block__cta-link',
				'a-cta',
				'a-cta--secondary',
				...$this->get_custom_secondary_cta_classes() ?? '',
			],
		] );
	}

	/**
	 * @return array
	 */
	public function get_image_args(): array {
		if ( ! $this->image ) {
			return [];
		}

		$srcset_sizes = [
			Image_Sizes::SQUARE_MEDIUM,
			Image_Sizes::SQUARE_LARGE,
		];

		if ( $this->layout === Media_Text_Block::MEDIA_CENTER ) {
			$srcset_sizes = [
				Image_Sizes::SIXTEEN_NINE_SMALL,
				Image_Sizes::SIXTEEN_NINE,
				Image_Sizes::SIXTEEN_NINE_LARGE,
			];
		}

		return [
			Image_Controller::IMG_ID       => $this->image,
			Image_Controller::SRCSET_SIZES => $srcset_sizes,
		];
	}

	/**
	 * @return string
	 */
	public function get_video_embed(): string {
		if ( ! $this->video ) {
			return '';
		}

		return $this->video;
	}

	/**
	 * @return string
	 */
	public function get_html_embed(): string {
		if ( ! $this->html ) {
			return '';
		}

		return $this->html;
	}

	/**
	 * @return string
	 */
	public function get_heading_tag(): string {
		if ( ! $this->heading_tag ) {
			return '';
		}

		return $this->heading_tag;
	}

	/**
	 * @return array
	 */
	public function get_buttons(): array {
		if ( empty( $this->buttons ) || $this->cta['url'] || $this->secondary_cta['url'] ) {
			return [];
		}

		return array_map( function ( $button ) {
			$link = wp_parse_args( $button[ Media_Text_Block::BUTTON_LINK ], [
				'title'  => '',
				'url'    => '',
				'target' => '',
			] );

			if ( empty( $link['url'] ) ) {
				return [];
			}

			$cta_id = 'button-' . sanitize_title( $link['title'] );

			if ( array_key_exists( Media_Text_Block::BUTTON_CTA_ID, $button ) && ! empty( $button[ Media_Text_Block::BUTTON_CTA_ID ] ) ) {
				$cta_id = sanitize_title( $button[ Media_Text_Block::BUTTON_CTA_ID ] );
			}

			return [
				Link_Controller::URL     => $link['url'],
				Link_Controller::CONTENT => $link['title'] ?? $link['url'],
				Link_Controller::TARGET  => $link['target'],
				Link_Controller::ID      => $cta_id,
				Link_Controller::CLASSES => $this->get_button_classes( $button ),
			];
		}, $this->buttons );
	}

	private function get_button_classes( $button ): array {
		$classes = [ 'b-media-text__button' ];

		switch ( $button[ Media_Text_Block::BUTTON_STYLE ] ) {
			case Media_Text_Block::STYLE_SECONDARY:
				$classes[] = sprintf( 'a-btn-%s', $button[ Media_Text_Block::BUTTON_STYLE ] );
				break;
			case Media_Text_Block::STYLE_CTA:
				$classes[] = 'a-cta';
				break;
			default:
				$classes[] = 'a-btn';
				break;
		}

		return $classes;
	}

	public function get_footer_content(): string {
		return $this->footer_content;
	}

}
