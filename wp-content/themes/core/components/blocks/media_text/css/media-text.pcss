/* -----------------------------------------------------------------------------
 *
 * Component: Block: Media + Text
 *
 * ----------------------------------------------------------------------------- */

.b-media-text {
	/* CASE: Width: Full - This is different from `full-bleed`. */

	&.c-block--width-full {
		max-width: none;
		width: 100%;
	}

	/* CASE: Two Media + Text Blocks Stacked, both Width: Full and Media: Left/Right */

	&.c-block--width-full:not(.c-block--layout-center) + &.c-block--width-full:not(.c-block--layout-center) {
		@media (--viewport-full) {
			margin-top: calc(-1 * var(--spacer-70));
		}
	}

	&__buttons-list {
		display: flex;
		flex-direction: row;
		grid-column-gap: var(--spacer-30);
		grid-row-gap: var(--spacer-10);
		align-items: center;
		flex-wrap: wrap;

		@media (--viewport-medium-max) {
			flex-direction: column;
			grid-gap: var(--spacer-10);
			align-items: flex-start;
		}

		.b-media-text__button {
			&.a-cta {
				&:not(:last-child) {
					@media (--viewport-full) {
						margin-right: var(--spacer-30);
					}
				}
			}
		}
	}

	&.c-block--layout-center {
		.b-media-text__buttons-list {
			justify-content: center;

			@media (--viewport-medium-max) {
				align-items: center;
			}
		}
	}
}

/* -----------------------------------------------------------------------------
 * Media + Text: Container
 * ----------------------------------------------------------------------------- */

.b-media-text__container {
	.c-block--layout-center & {
		display: flex;
		flex-direction: column;
	}

	.c-block--layout-center--inversed & {
		flex-direction: column-reverse;
	}

	/* CASE: Width: Grid, Media: Left/Right */

	.c-block--width-grid:not(.c-block--layout-center) & {
		@media (--viewport-medium) {
			display: flex;
			justify-content: flex-start;
			align-items: center;
		}

		.b-media-text__content {
			width: 100%;
		}
	}

	/* CASE: Width: Full */

	.c-block--width-full & {
		max-width: var(--grid-width-full-bleed);
		margin: 0 auto;
	}

	/* CASE: Width: Full, Media: Left/Right */

	.c-block--width-full:not(.c-block--layout-center) & {
		@media (--viewport-full) {
			display: flex;
			align-items: center;
		}
	}

	/* CASE: Media: Right */

	.c-block--layout-right &,
	.c-block--layout-right_list & {
		@media (--viewport-medium) {
			flex-direction: row-reverse;
		}
	}

	/* CASE: Width: Grid + Content */

	.c-block--width-content & {
		padding-left: 0;
		padding-right: 0;
		max-width: var(--grid-width-staggered-double);
		width: calc(100% - var(--grid-gutter-small)*2);

		@media (--viewport-medium) {
			width: calc(100% - 2 * var(--grid-gutter));
		}
	}
}

/* -----------------------------------------------------------------------------
 * Media + Text: Media
 * ----------------------------------------------------------------------------- */

.b-media-text__media {
	margin-bottom: var(--spacer-30);

	@media (--viewport-medium) {
		margin-bottom: var(--spacer-40);
	}

	.c-block--layout-center--inversed & {
		margin-bottom: 0;

		margin-top: var(--spacer-30);

		@media (--viewport-medium) {
			margin-top: var(--spacer-40);
		}
	}

	/* CASE: Width: Grid, Media: Left/Right */

	.c-block--width-grid:not(.c-block--layout-center) & {
		@media (--viewport-medium) {
			flex: 0 0 auto;
			width: calc(50% - var(--grid-gutter-half));
			margin-bottom: 0;
		}

		iframe {
			max-height: 246px;

			@media (--viewport-full) {
				max-height: 438px;
			}
		}
	}

	/* CASE: Width: Full, Media: Left/Right */

	.c-block--width-full:not(.c-block--layout-center) & {
		@media (--viewport-full) {
			flex: 0 0 auto;
			width: 50%;
		}

		iframe {
			max-height: 438px;
		}
	}

	/* CASE: Width: Full, Media: Center */

	.c-block--layout-center & {
		display: flex;
		justify-content: center;

		iframe {
			max-height: 620px;
		}
	}

	> div,
	> iframe,
	.c-image__image {
		width: 100%;
		max-width: var(--grid-10-col);
	}

	/* CASE: Embed media type */

	&.b-media-text__media--embed {

		iframe {
			width: 100%;
			height: calc(100vw / (16 / 9));
		}
	}

	.c-block--media-size-medium.c-block--width-grid:not(.c-block--layout-center) &,
	.c-block--media-size-medium.c-block--width-full:not(.c-block--layout-center) & {
		@media (--viewport-medium) {
			width: calc(33% - var(--grid-gutter-half));
		}

		@media (--viewport-full) {
			width: var(--grid-4-col);
		}
	}

	.c-block--media-size-small.c-block--width-grid:not(.c-block--layout-center) &,
	.c-block--media-size-small.c-block--width-full:not(.c-block--layout-center) & {
		@media (--viewport-medium) {
			width: calc(25% - var(--grid-gutter-half));
		}

		@media (--viewport-full) {
			width: var(--grid-2-col);
		}
	}

	/* -----------------------------------------------------------------------------
	 * Media + Text: Media: Image
	 * ----------------------------------------------------------------------------- */

	.c-image__image {
		width: 100%;
	}
}

/* -----------------------------------------------------------------------------
 * Media + Text: Content
 * ----------------------------------------------------------------------------- */

.b-media-text__content {
	margin-top: var(--spacer-30);

	@media (--viewport-medium) {
		margin-top: 0;
	}

	/* CASE: Media: Left/Right */

	.c-block:not(.c-block--layout-center) & {
		@media (--viewport-medium) {
			align-self: center;
		}
	}

	/* CASE: Width: Grid, Media: Left/Right */

	.c-block--width-grid:not(.c-block--layout-center) & {

	}

	/* CASE: Width: Full, Media: Left/Right */

	.c-block--width-full:not(.c-block--layout-center) & {
		@media (--viewport-full) {
			display: flex;
			flex: 0 0 auto;
			width: 50%;
			max-width: calc(var(--grid-gutter-half) + var(--grid-6-col) + var(--grid-margin));
			margin: 0;
		}
	}

	/* CASE: Media: Left */

	.c-block--width-grid.c-block--layout-left &,
	.c-block--width-grid.c-block--layout-left_list & {
		@media (--viewport-medium) {
			justify-content: flex-end;
			padding-left: var(--grid-gutter);
		}
	}

	/* CASE: Media: Right */

	.c-block--layout-right &,
	.c-block--layout-right_list & {
		@media (--viewport-medium) {
			padding-right: var(--grid-gutter);
		}
	}

	/* We need to do this way to get over the specificity of the h2 default */

	.page #main-content > .l-sink > .wp-block-group .c-block.b-media-text.c-block--media-size-small.c-block--width-grid:not(.c-block--layout-center) .b-media-text__container &,
	.page #main-content > .l-sink > .wp-block-group .c-block.b-media-text.c-block--media-size-small.c-block--width-full:not(.c-block--layout-center) .b-media-text__container &,
	.page #main-content > .l-sink > .wp-block-group .c-block.b-media-text.c-block--media-size-medium.c-block--width-grid:not(.c-block--layout-center) .b-media-text__container &,
	.page #main-content > .l-sink > .wp-block-group .c-block.b-media-text.c-block--media-size-medium.c-block--width-full:not(.c-block--layout-center) .b-media-text__container &,
	.page #main-content > .l-sink > .c-block.b-media-text.c-block--media-size-small.c-block--width-grid:not(.c-block--layout-center) .b-media-text__container &,
	.page #main-content > .l-sink > .c-block.b-media-text.c-block--media-size-small.c-block--width-full:not(.c-block--layout-center) .b-media-text__container &,
	.page #main-content > .l-sink > .c-block.b-media-text.c-block--media-size-medium.c-block--width-grid:not(.c-block--layout-center) .b-media-text__container &,
	.page #main-content > .l-sink > .c-block.b-media-text.c-block--media-size-medium.c-block--width-full:not(.c-block--layout-center) .b-media-text__container & {
		.h3.b-media-text__title {
			@media (--viewport-full) {
				@mixin t-display-large;
			}
		}
	}

	.c-block--media-size-medium.c-block--width-grid:not(.c-block--layout-center) &,
	.c-block--media-size-medium.c-block--width-full:not(.c-block--layout-center) & {
		width: 100%;
	}

	.c-block--media-size-small.c-block--width-grid:not(.c-block--layout-center) &,
	.c-block--media-size-small.c-block--width-full:not(.c-block--layout-center) & {
		width: 100%;

		.c-content-block {
			@media (--viewport-full) {
				max-width: 100%;
			}
		}
	}

	&.b-media-text--includes-h1 {
		.c-block__header {
			h2 {
				font-size: var(--font-size-heading);
				line-height: var(--line-height-heading);
			}
		}
	}

	.b-links {
		margin-top: 0;

		&__content {
			width: 100%;
		}

		&__container.l-container {
			padding-left: 0;
			padding-right: 0;
		}

		.list-type--checklist .b-links__list-item:before {
			background-color: transparent;
			margin-right: var(--spacer-20);
		}

        &__list-item,
		&__list-item~li {
			margin-top: 0;
			margin-bottom: var(--spacer-10);
		}

		&__list-item p {
			font-weight: var(--font-weight-regular);
		}

		.list-type--checklist .b-links__list-item {
			grid-template-columns: var(--spacer-30) auto;
		    
			&:before {
				font-size: var(--font-size-heading);
				width: var(--spacer-30);
				margin-right: 0;
			}
		}
	}
}

/* -----------------------------------------------------------------------------
 * Media + Text: Content: Container
 * ----------------------------------------------------------------------------- */

.b-media-text__content-container {
	/* CASE: Width: Grid, Media: Left/Right */

	.c-block--width-grid:not(.c-block--layout-center) & {
		@media (--viewport-medium) {
			margin-top: 0;
		}
	}

	/* CASE: Width: Full, Media: Left/Right */

	.c-block--width-full:not(.c-block--layout-center) & {
		@media (--viewport-full) {
			width: 100%;
			max-width: var(--grid-5-col);
			margin-top: 0;
		}
	}

	.wp-block-group & {

		.c-content-block__cta:first-of-type {
			margin-top: var(--spacer-30);
			margin-bottom: 0;
		}
	}
}

/* -----------------------------------------------------------------------------
 * Media + Text: Variation
 * ----------------------------------------------------------------------------- */
#main-content .item-single--perfect-post-layout {
	.b-media-text {
		&.c-block--width-grid:not(.c-block--layout-center) .b-media-text__media {
			@media (--viewport-medium) {
				width: var(--grid-4-col);
			}
		}

		&:not(.c-block--layout-center).c-block--media-size {
			&-regular {
				.b-media-text__media {
					@media (--viewport-medium) {
						width: var(--grid-3-col);
					}
				}
			}

			&-medium {
				.b-media-text__media {
					@media (--viewport-medium) {
						width: var(--grid-2-col);
					}
				}
			}

			&-small {
				.b-media-text__media {
					@media (--viewport-medium) {
						width: var(--grid-2-col);
					}
				}
			}
		}

		&.c-block--layout-center {
			.b-media-text__media {
				@media (--viewport-medium) {
					max-width: var(--grid-6-col);
					margin-left: auto;
					margin-right: auto;
				}
			}
		}

		.b-media-text__content {
			.h3.c-block__title {
				@media (--viewport-full) {
					@mixin t-display;
				}
			}
		}

		&.c-block--width-content {
			.b-media-text__container {
				width: 100%;
				padding-left: 0;
				padding-right: 0;

				@media (--viewport-medium) {
					width: calc(100% - var(--grid-gutter)*2);
				}

				@media (--viewport-full) {
					width: 100%;
					padding-left: 0;
					padding-right: 0;
				}
			}
		}
	}

	.l-sink > .wp-block-group {
		.c-block.b-media-text {
			@media (--viewport-medium) {
				margin-top: 0;
				margin-bottom: 0;
			}
		}
	}
}
