/* -----------------------------------------------------------------------------
 *
 * Component: Countdown
 *
 * ----------------------------------------------------------------------------- */

.c-countdown {

	.number {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		line-height: 40px;
		font-weight: var(--font-weight-semibold);
		font-size: var(--global-font-size-heading-xxsmall);

		@media (--viewport-full) {
			line-height: 30px;
		}
	}

	.new-number {
		color: var(--color-neutral-30);
		opacity: 0;
		animation-name: newNumberColor;
  		animation-duration: 2s;
	}

	.old-number {
		opacity: 1;
		margin-top: -30px;
	}
}

@keyframes newNumberColor {
	0% {
		color: var(--color-neutral-30);
	}
	70% {
		color: var(--color-alert-negative);
	}
}

.c-countdown__timer-container {
	background-color: var(--color-white);
	border-radius: var(--border-radius-media);
	padding: var(--spacer-30) 12px;

	@media (--viewport-medium) {
		padding: var(--spacer-40);
	}

	@media (--viewport-full) {
		padding: var(--spacer-40) var(--spacer-20);
	}

	@media (--viewport-large) {
		padding: var(--spacer-40);
	}
}

.c-countdown__title {
	@mixin t-overline;

	text-align: center;
	margin-bottom: var(--spacer-20);

	&:last-child {
		margin-bottom: 0;
	}

	/* CASE: Used in a group block with dark background */
	.t-sink > .wp-block-group & {
		@mixin t-overline;

		color: var(--color-black);
		margin-bottom: var(--spacer-20);

		&:last-child {
			margin-bottom: 0;
		}
	}
}

.c-countdown__items {
	display: flex;
	flex-flow: row nowrap;
	justify-content: center;
}

.c-countdown__item {
	display: flex;
	flex-flow: column nowrap;
	justify-items: center;
	align-items: center;

	&:not(:last-child) {
		margin-right: 10px;

		@media (--viewport-medium) {
			margin-right: 20px;
		}

		.c-countdown__unit {
			position: relative;

			&:before {
				content: ":";
				position: absolute;
				right: -8px;
				/* top: 50%;
				transform: translateY(-50%); */
				color: var(--color-black);

				@mixin t-display-x-small;
				top: 5px;

				@media (--viewport-medium) {
					right: -13px;
					top: 0;

					@mixin t-display-small;
				}
			}
		}
	}

	&:nth-last-child(2){
		.c-countdown__unit {
			&:before {
				right: -12px;

				@media (--viewport-medium) {
					right: -20px;
				}
			}
		}
	}

	&--days {
		.c-countdown__items--less-than-day & {
			display: none;
		}
	}

	&--minutes,
	&--seconds {
		.c-countdown__items--less-than-hour & {
			.c-countdown__unit {
				color: var(--color-alert-negative);
			}
		}
	}
}

.c-countdown__unit {
	@mixin t-display-x-small;

	display: flex;
	font-weight: var(--font-weight-semibold);
	
	@mixin t-display-small;

	@media (--viewport-full) {
		@mixin t-display-x-small;
	}

	@media (--viewport-large) {
		@mixin t-display-small;

		font-size: 28px;
	}
}

.c-countdown__digit {
	padding: 0;
	text-align: center;
	overflow: hidden;
	width: 15px;
	height: 40px;

	&:first-child:not(:last-child) {
		margin-right: 2px;
	}

	/* CASE: Used in a group block with dark background */
	.t-sink > .wp-block-group.has-dark-background-color & {

		.number {
			color: var(--color-black);
		}
	}
}

.c-countdown__format {
	@mixin t-caption;

	margin-top: var(--spacer-10);
	font-size: 11px;

	/* CASE: Used in a group block with dark background */
	.t-sink > .wp-block-group.has-dark-background-color & {
		@mixin t-caption;

		margin-top: var(--spacer-10);
		color: var(--color-neutral-50);
	}
}
