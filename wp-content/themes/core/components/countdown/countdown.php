<?php
declare( strict_types=1 );

use Tribe\Project\Templates\Components\blocks\countdown\Countdown_Block_Controller;
use Tribe\Project\Templates\Components\countdown\Countdown_Controller;

/**
 * @var array $args Arguments passed to the template
 */
// phpcs:ignore VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable
$c = Countdown_Block_Controller::factory( $args );
?>

<div class="c-countdown <?php echo Countdown_Controller::HIDE_COUNTDOWN ? 'u-hidden' : '' ?>" data-js="countdown-clock" data-target-date="<?php echo $args[ Countdown_Controller::END_DATE ]; ?>"
	data-countdown="<?php echo Countdown_Controller::COUNTDOWN; ?>">
	<div class="c-countdown__timer-container">
		<p class="c-countdown__title"><?php _e( 'Starting in', 'tribe' ); ?></p>
		<div class="c-countdown__items">
			<div class="c-countdown__item c-countdown__item--days">
				<div class="c-countdown__days c-countdown__unit"></div>
				<div class="c-countdown__format"><?php _e( 'Days', 'tribe' ); ?></div>
			</div>
			<div class="c-countdown__item c-countdown__item--hours">
				<div class="c-countdown__hours c-countdown__unit"></div>
				<div class="c-countdown__format"><?php _e( 'Hours', 'tribe' ); ?></div>
			</div>
			<div class="c-countdown__item c-countdown__item--minutes">
				<div class="c-countdown__minutes c-countdown__unit"></div>
				<div class="c-countdown__format"><?php _e( 'Minutes', 'tribe' ); ?></div>
			</div>
			<div class="c-countdown__item c-countdown__item--seconds">
				<div class="c-countdown__seconds c-countdown__unit"></div>
				<div class="c-countdown__format"><?php _e( 'Seconds', 'tribe' ); ?></div>
			</div>
		</div>
	</div>
</div>
