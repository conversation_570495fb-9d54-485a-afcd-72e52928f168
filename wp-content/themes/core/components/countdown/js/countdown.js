/**
 * @module Countdown Panel
 * @description Controls the countdown clock for the panel
 */

import _ from 'lodash';
import * as tools from 'utils/tools';

const { countdown } = window;
const el = {
	countdownContainer: tools.getNodes( '.c-countdown__items', true, document, true )[ 0 ],
	lessThanHourClass: 'c-countdown__items--less-than-hour',
	lessThanDayClass: 'c-countdown__items--less-than-day',
};
const instances = {
	countdowns: {},
};

const countdownState = {
	invalid: 'invalid',
	upcoming: 'upcoming',
	complete: 'complete',
};

/**
 * @function formatNumber
 * @description Accepts a number and converts it to a string with each digit wrapped in a <span>
 * @param currentTime
 * @param previousTime
 * @returns {string}
 */

const formatNumber = ( currentTime, previousTime ) => {
	const previousTimeArray = previousTime.toString().split( '' );
	const currentTimeArray  = currentTime.toString().split( '' );
	let formattedNumber = '';

	// Make sure times are always at least 2 digits
	if ( previousTimeArray.length === 1 ) {
		previousTimeArray.unshift( '0' );
	}

	// Make sure times are always at least 2 digits
	if ( currentTimeArray.length === 1 ) {
		currentTimeArray.unshift( '0' );
	}

	// Loop through digits in current number and format
	currentTimeArray.forEach( ( digit, index ) => {
		// If digit has changed from before, add elements to trigger animation
		if ( digit !== previousTimeArray[ index ] && previousTime !== '' ) {
			formattedNumber +=
				`<div class="c-countdown__digit">
					<div class="number new-number">${ digit }</div>
			        <div class="number old-number">${ previousTimeArray[ index ] }</div>
				</div>`;
		} else {
			formattedNumber +=
				`<div class="c-countdown__digit">
					<div class="number">${ digit }</div>
				</div>`;
		}
	} );

	return formattedNumber;
};

/**
 * Trigger jQuery animation to replace old digit
 */
const animateTimeChange = () => {
	// eslint-disable-next-line no-undef
	const newNum = jQuery( '.new-number' );
	// eslint-disable-next-line no-undef
	const oldNum = jQuery( '.old-number' );

	oldNum.animate(
		{
			marginTop: '10',
			opacity: '0',
		},
		{
			duration: 1000,
			step: () => {
				newNum.animate( { opacity: '1' } );
			},
		}
	);
};

/**
 * Display a message that this date has passed.
 */
const showCountdownOverHtml = ( counter, messageText = 'This event has passed.' ) => {
	const countdownContainer = tools.getNodes( '.c-countdown__timer-container', true, counter, true )[ 0 ];

	countdownContainer.innerHTML = `<p class="c-countdown__title">${ messageText }</p>`;
};

/**
 * @function updateUI
 * @description
 */

const updateUI = ( ts, counter, id ) => {
	// Get previous time
	const previousTime = {
		days: counter.dataset.days,
		hours: counter.dataset.hours,
		minutes: counter.dataset.minutes,
		seconds: counter.dataset.seconds,
	};

	// Shows a custom message if event changes from starting to complete, i.e. event time plus one minute.
	if ( parseInt( previousTime.days + previousTime.hours + previousTime.minutes + previousTime.seconds ) === 0 ) {
		showCountdownOverHtml( counter );

		return false;
	}

	if ( ts.days === 0 && ts.hours === 0 && ! el.countdownContainer.classList.contains( el.lessThanHourClass ) ) {
		el.countdownContainer.classList.add( el.lessThanHourClass );
	}

	if ( ts.days === 0 && ! el.countdownContainer.classList.contains( el.lessThanDayClass ) ) {
		el.countdownContainer.classList.add( el.lessThanDayClass );
	}

	el[ id ].days.innerHTML = formatNumber( ts.days, previousTime.days );
	el[ id ].hours.innerHTML = formatNumber( ts.hours, previousTime.hours );
	el[ id ].minutes.innerHTML = formatNumber( ts.minutes, previousTime.minutes );
	el[ id ].seconds.innerHTML = formatNumber( ts.seconds, previousTime.seconds );

	animateTimeChange();

	// Store current time as data attribute
	counter.dataset.days = ts.days;
	counter.dataset.hours = ts.hours;
	counter.dataset.minutes = ts.minutes;
	counter.dataset.seconds = ts.seconds;

	// Shows a custom message if timer hits zero
	if ( parseInt( counter.dataset.days + counter.dataset.hours + counter.dataset.minutes + counter.dataset.seconds ) === 0 ) {
		showCountdownOverHtml( counter, 'This event is starting.' );

		return false;
	}
};

/**
 * @function cacheCounterElements
 * @description
 */

const cacheCounterElements = ( counter, id ) => {
	el[ id ] = {
		days: counter.querySelector( '.c-countdown__days' ),
		hours: counter.querySelector( '.c-countdown__hours' ),
		minutes: counter.querySelector( '.c-countdown__minutes' ),
		seconds: counter.querySelector( '.c-countdown__seconds' ),
	};
};

/**
 * @function getStartAndEndDates
 * @description
 */

const getEndDate = ( counter ) => {
	const targetDate = counter.getAttribute( 'data-target-date' );
	const targetTime = counter.getAttribute( 'data-countdown' );
	const now = new Date().getTime();

	if ( ! targetDate && ! targetTime ) {
		showCountdownOverHtml( counter, '' );
		return {
			state: countdownState.invalid,
			date: null,
		};
	}

	if ( targetDate ) {
		const dateObj = new Date( targetDate );

		if ( dateObj.getTime() > now ) {
			return {
				state: countdownState.upcoming,
				date: dateObj,
			};
		}

		showCountdownOverHtml( counter );

		return {
			state: countdownState.complete,
			date: dateObj,
		};
	}

	if ( targetTime ) {
		return {
			state: countdownState.upcoming,
			date: new Date( now + parseInt( targetTime ) * 1000 ),
		};
	}
};

/**
 * @function countdownKickoff
 * @description
 *
 */

const countdownKickoff = () => {
	el.counters.forEach( ( counter ) => {
		if ( counter.classList.contains( 'initialized' ) ) {
			return;
		}
		const id = _.uniqueId( 'counter-' );
		const endDate = getEndDate( counter );

		if ( endDate.state === countdownState.invalid ) {
			return;
		}

		// Adding a class to the block to hide seats left and date/time elements
		if ( endDate.state === countdownState.complete && counter.closest( '.b-countdown' ) ) {
			counter.closest( '.b-countdown' ).classList.add( 'countdown-expired' );
		}

		cacheCounterElements( counter, id );

		// Initialize time data attributes
		counter.dataset.days = '';
		counter.dataset.hours = '';
		counter.dataset.minutes = '';
		counter.dataset.seconds = '';
		counter.dataset.endDate = endDate.date;

		if ( endDate.state === countdownState.upcoming ) {
			instances.countdowns[ id ] = countdown( ( ts ) => updateUI( ts, counter, id ), endDate.date, countdown.DAYS | countdown.HOURS | countdown.MINUTES | countdown.SECONDS );
			counter.classList.add( 'initialized' );
		}
	} );
};

/**
 * @function
 * @description
 */

const cacheElements = () => {
	el.counters = tools.getNodes( '[data-js="countdown-clock"]:not(.initialized)', true, document, true );
};

/**
 * @module
 * @description Responds to panel live updating.
 */

const previewChangeHandler = () => {
	cacheElements();
	countdownKickoff();
};

/**
 * @module
 * @description Bind Events.
 */

const bindEvents = () => {
	// Re-Initialize sliders after block preview changes
	if ( window.acf ) {
		window.acf.addAction( 'render_block_preview', previewChangeHandler );
	}
};

const init = () => {
	cacheElements();
	countdownKickoff();
	bindEvents();

	console.info( 'SquareOne Theme: Initialized Countdown Timer' );
};

export default init;
