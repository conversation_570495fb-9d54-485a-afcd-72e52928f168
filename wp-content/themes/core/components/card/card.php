<?php
declare( strict_types=1 );

use \Tribe\Project\Templates\Components\card\Card_Controller;

/**
 * @var array $args Arguments passed to the template
 */
// phpcs:ignore VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable
$c = Card_Controller::factory( $args );
?>

<<?php echo $c->get_tag(); ?> <?php echo $c->get_classes(); ?> <?php echo$c->get_attrs(); ?>>

	<?php if ( ! empty( $c->render_image() ) ) { ?>
		<div <?php echo $c->get_media_classes(); ?>>
			<?php if ( $c->has_members_only_on_top() && $c->get_memberonly_status() && ! $c->hide_members_only_note() ) : ?>
				<div class="c-card__members-only-locked t-overline">
					<span class="icon icon-lock"></span>
					<?php _e( 'Members Exclusive', 'tribe' ); ?>
				</div>
			<?php endif; ?>
			<?php if ( ! $c->has_members_only_on_top() ) {
				get_template_part( 'components/badges/badge_watched', null, $c->get_watched_badge() );
			} ?>
			<?php echo $c->render_image(); ?>
		</div>
	<?php } ?>

	<div <?php echo $c->get_content_classes(); ?>>
		<?php if ( ! $c->hide_meta() && ( ! $c->hide_topic() && ! empty( $c->get_main_category() ) ) ) : ?>
			<div class="c-card__meta-primary-container">
				<?php if ( ! $c->hide_topic() && $c->get_main_category() ) : ?>
					<?php get_template_part( 'components/container/container', null, $c->get_main_category() ); ?>
				<?php endif; ?>
				<?php echo $c->get_separator_element(); ?>
				<?php if ( $c->render_duration() ) :
					 echo $c->render_duration();
				else :
					 echo $c->render_post_type_name();
				endif; ?>
			</div>
		<?php endif; ?>

		<?php if ( ! empty( $c->get_product_tag() ) ) : ?>
			<div class="c-card__product-tag">
				<?php echo $c->get_product_tag(); ?>
			</div>
		<?php endif; ?>

		<?php echo $c->render_title(); ?>

		<?php if ( $c->has_meta_secondary() ) { ?>
			<?php echo $c->render_meta_secondary(); ?>
		<?php } ?>

		<?php if ( $c->is_upcoming_event() && ! $c->hide_meta() && $date_raw = $c->get_event_date_raw() ) { ?>
			<div class="c-card__date-time-container event-date-container" data-date="<?php echo esc_attr( $date_raw ); ?>">
				<?php if ( $date = $c->get_event_date() ) { ?>
					<div class="c-card__date">
						<i class="icon icon-calendar"></i>
						<span class="event-date-day"><?php echo esc_html( $date ); ?></span>
					</div>
				<?php } ?>

				<?php if ( $time = $c->get_event_time() ) { ?>
					<div class="c-card__time">
						<i class="icon icon-clock"></i>
						<span class="event-date-time"><?php echo esc_html( $time ); ?></span>
					</div>
				<?php } ?>
			</div>
		<?php } ?>

		<?php echo $c->render_description(); ?>

		<?php if ( ! $c->hide_author() ) { ?>
			<?php if ( $c->is_multi_authors() ) : ?>
				<div class="item-single__author-container item-single--multiauthor">
					<?php echo $c->render_multiple_authors(); ?>
				</div>
			<?php else :
				echo $c->render_author();
			endif; ?>
		<?php } ?>

		<?php if ( $c->is_upcoming_event() && ! $c->hide_meta() ) { ?>
			<div class="c-card__upcoming-cta">
				<?php get_template_part( 'components/link/link', null, $c->get_upcoming_event_link() ); ?>
			</div>
		<?php } ?>

		<?php if ( $c->get_price_regular() || $c->get_price_discounted() ) : ?>
			<div class="c-card__price">
				<?php echo $c->get_price_regular(); ?>
				<?php echo $c->get_price_discounted(); ?>
				<?php echo $c->get_deal_tag(); ?>
			</div>
		<?php endif; ?>

		<?php if ( $c->get_card_note() ) : ?>
			<div class="c-card__note">
				<?php get_template_part( 'components/container/container', null, $c->get_cta_args() ); ?>

				<div class="c-card__note__content">
					<?php echo $c->get_card_note(); ?>
				</div>
			</div>
		<?php else : ?>
			<?php get_template_part( 'components/container/container', null, $c->get_cta_args() ); ?>
		<?php endif; ?>

		<?php if ( ! $c->is_dashboard() && $c->get_memberonly_status() && ! $c->hide_members_only_note() && ! $c->has_members_only_on_top() ) { ?>
			<div class="c-card__members-only-warning">
				<?php _e( 'Members Only Content', 'tribe' ); ?>
			</div>
		<?php } ?>

	</div>

</<?php echo $c->get_tag(); ?>>
