/** -----------------------------------------------------------------------------
 *
 * Component: Card
 *
 * Scripts specific to the card component.
 *
 * ----------------------------------------------------------------------------- */

import delegate from 'delegate';
import * as tools from 'utils/tools';
import { convertToLocalTimezone, timezoneFormat, timezoneLocale } from 'utils/data/local-timezone';

/* Maximum amount of time between mousedown & mouseup to be considered a true click */
const MOUSEUP_THRESHOLD = 200;

/* Elements that should be excluded when handling the card click-within */
const EXCLUDED_TARGETS = [ 'A', 'BUTTON' ];

const el = {
	siteWrap: document,
	eventDateContainers: tools.getNodes( '.c-card__date-time-container', true, document, true ),
};

const state = {
	down: 0,
	up: 0,
};

/**
 * handleCardClick
 *
 * Finds the relevant target link and triggers location change to that URL.
 *
 * @param e
 */
const handleCardClick = ( e ) => {
	const targetLinkEl = e.delegateTarget.querySelector( '[data-js="target-link"]' );

	if ( targetLinkEl && targetLinkEl.hasAttribute( 'href' ) ) {
		const url = targetLinkEl.getAttribute( 'href' );

		if ( ! url ) {
			return;
		}

		if ( ! url.startsWith( '#modal-id-' ) ) {
			( e.ctrlKey || targetLinkEl.hasAttribute( 'newwindow' ) ) ? window.open( url ) : window.location = url;
		}
	}
};

/**
 * handleCardMouseDown
 *
 * Sets a timestamp on mousedown for cards for testing text selection.
 *
 * @param e
 */
const handleCardMouseDown = ( e ) => {
	if ( EXCLUDED_TARGETS.includes( e.target.nodeName ) ) {
		return;
	}

	state.down = new Date();
};

/**
 * handleCardMouseUp
 *
 * Checks the amount of time past since mousedown and triggers a click
 * if the time past is less than the threshold.
 *
 * @param e
 */
const handleCardMouseUp = ( e ) => {
	if ( EXCLUDED_TARGETS.includes( e.target.nodeName ) ) {
		return;
	}

	state.up = new Date();

	if ( state.up - state.down < MOUSEUP_THRESHOLD ) {
		handleCardClick( e );
	}
};

const handleEventDates = () => {
	if ( ! el.eventDateContainers ) {
		return;
	}

	el.eventDateContainers.forEach( ( container ) => {
		const eventDay = tools.getNodes( '.event-date-day', true, container, true );
		const eventTime = tools.getNodes( '.event-date-time', true, container, true );

		if ( 1 === eventDay.length && 1 === eventTime.length && container.dataset.date ) {
			eventDay[ 0 ].innerHTML = convertToLocalTimezone( container.dataset.date, timezoneFormat.WEEKDAY_MONTH_DAY, timezoneLocale.EN );
			eventTime[ 0 ].innerHTML = convertToLocalTimezone( container.dataset.date, timezoneFormat.TIME_AM_PM, timezoneLocale.EN );
		}
	} );
};

/**
 * bindEvents
 */
const bindEvents = () => {
	delegate( el.siteWrap, '[data-js="use-target-link"]', 'mousedown', handleCardMouseDown );
	delegate( el.siteWrap, '[data-js="use-target-link"]', 'mouseup', handleCardMouseUp );

	handleEventDates();
};

/**
 * init
 */
const init = () => {
	bindEvents();

	console.info( 'SquareOne Theme: Initialized card component scripts.' );
};

export default init;
