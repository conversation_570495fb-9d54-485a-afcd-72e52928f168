<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\card;

use DateTime;
use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Object_Meta\Member_Stream_Meta;
use Tribe\Project\Post_Types\Member_Stream\Member_Stream;
use Tribe\Project\Taxonomies\Post_State\Post_State;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Templates\Components\author\Author_Controller;
use Tribe\Project\Templates\Components\badges\Badges_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\Traits\Handles_MemberPress_Permissions;
use Tribe\Project\Templates\Components\Traits\Handles_Post_Types;

class Card_Controller extends Abstract_Controller {
	use Handles_Post_Types;
	use Handles_MemberPress_Permissions;

	public const TAG                = 'tag';
	public const CLASSES            = 'classes';
	public const ATTRS              = 'attrs';
	public const IMAGE              = 'image';
	public const MEDIA_CLASSES      = 'media_classes';
	public const CONTENT_CLASSES    = 'content_classes';
	public const META_SECONDARY     = 'meta_secondary';
	public const TITLE              = 'title';
	public const DESCRIPTION        = 'description';
	public const CTA                = 'cta';
	public const USE_TARGET_LINK    = 'use_target_link';
	public const AUTHOR             = 'author';
	public const MULTI_AUTHORS      = 'multi_authors';
	public const DURATION           = 'duration';
	public const POST_ID            = 'post_id';
	public const POST_TYPE_NAME     = 'post_type_name';
	public const MAIN_CATEGORY      = 'main_category';
	public const QUERY_TYPE         = 'query_type';
	public const QUERY_TYPE_MANUAL  = 'manual';
	public const QUERY_TYPE_AUTO    = 'auto';
	public const HIDE_TOPIC         = 'hide_topic';
	public const HIDE_AUTHOR        = 'hide_author';
	public const HIDE_AUTHORS_IMAGE = 'hide_authors_image';
	public const HIDE_META          = 'hide_meta';
	public const HIDE_MEMBERS_ONLY  = 'hide_members_only';
	public const TOP_MEMBERS_ONLY   = 'top_members_only';
	public const PRICE_REGULAR      = 'price_regular';
	public const PRICE_DISCOUNTED   = 'price_discounted';
	public const DEAL_TAG           = 'deal_tag';
	public const CARD_NOTE          = 'card_note';
	public const PRODUCT_TAG        = 'product_tag';

	public const STYLE          = 'style';
	public const STYLE_PLAIN    = 'plain';
	public const STYLE_ELEVATED = 'elevated';
	public const STYLE_OUTLINED = 'outlined';
	public const STYLE_INLINE   = 'inline';

	private string $tag;
	private array  $classes;
	private array  $attrs;
	private array  $media_classes;
	private array  $content_classes;
	private string $style;
	private bool   $use_target_link;
	private string $query_type;
	private bool   $hide_topic;
	private bool   $hide_author;
	private bool   $hide_authors_image;
	private bool   $hide_meta;
	private bool   $hide_members_only;
	private bool   $top_members_only;
	private int    $post_id;
	private array  $multi_authors;
	private string $price_regular;
	private string $price_discounted;
	private string $deal_tag;
	private string $card_note;

	/**
	 * @var null|Deferred_Component
	 * @uses components/image
	 */
	private ?Deferred_Component $image;

	/**
	 * @var null|Deferred_Component
	 * @uses components/container
	 */
	private ?Deferred_Component $meta_secondary;

	/**
	 * @var null|Deferred_Component
	 * @uses components/text
	 */
	private ?Deferred_Component $title;

	/**
	 * @var null|Deferred_Component
	 * @uses components/container
	 */
	private ?Deferred_Component $description;

	/**
	 * @var null|Deferred_Component
	 * @uses components/link
	 */
	private ?Deferred_Component $cta;

	/**
	 * @var null|Deferred_Component
	 * @uses components/container
	 */
	private ?Deferred_Component $author;

	/**
	 * @var null|Deferred_Component
	 * @uses components/container
	 */
	private ?Deferred_Component $duration;

	/**
	 * @var null|Deferred_Component
	 * @uses components/container
	 */
	private ?Deferred_Component $post_type_name;

	/**
	 * @var null|Deferred_Component
	 * @uses components/link
	 */
	private ?Deferred_Component $main_category;

	/**
	 * @var null|Deferred_Component
	 * @uses components/container
	 */
	private ?Deferred_Component $product_tag;

	public function __construct( array $args = [] ) {
		$args = $this->parse_args( $args );

		$this->tag                = (string) $args[ self::TAG ];
		$this->classes            = (array) $args[ self::CLASSES ];
		$this->attrs              = (array) $args[ self::ATTRS ];
		$this->media_classes      = (array) $args[ self::MEDIA_CLASSES ];
		$this->content_classes    = (array) $args[ self::CONTENT_CLASSES ];
		$this->style              = (string) $args[ self::STYLE ];
		$this->use_target_link    = (bool) $args[ self::USE_TARGET_LINK ];
		$this->image              = $args[ self::IMAGE ];
		$this->author             = $args[ self::AUTHOR ];
		$this->duration           = $args[ self::DURATION ];
		$this->meta_secondary     = $args[ self::META_SECONDARY ];
		$this->title              = $args[ self::TITLE ];
		$this->description        = $args[ self::DESCRIPTION ];
		$this->cta                = $args[ self::CTA ];
		$this->post_id            = $args[ self::POST_ID ];
		$this->post_type_name     = $args[ self::POST_TYPE_NAME ];
		$this->main_category      = $args[ self::MAIN_CATEGORY ];
		$this->query_type         = $args[ self::QUERY_TYPE ];
		$this->hide_topic         = $args[ self::HIDE_TOPIC ];
		$this->hide_author        = $args[ self::HIDE_AUTHOR ];
		$this->hide_authors_image = $args[ self::HIDE_AUTHORS_IMAGE ];
		$this->hide_meta          = $args[ self::HIDE_META ];
		$this->hide_members_only  = $args[ self::HIDE_MEMBERS_ONLY ];
		$this->top_members_only   = $args[ self::TOP_MEMBERS_ONLY ];
		$this->price_regular      = $args[ self::PRICE_REGULAR ];
		$this->price_discounted   = $args[ self::PRICE_DISCOUNTED ];
		$this->deal_tag           = $args[ self::DEAL_TAG ];
		$this->card_note          = $args[ self::CARD_NOTE ];
		$this->product_tag        = $args[ self::PRODUCT_TAG ];

		if ( array_key_exists( self::MULTI_AUTHORS, $args ) ) {
			$this->multi_authors = (array) $args[ self::MULTI_AUTHORS ] ?? [];
		} else {
			$this->multi_authors = [];
		}
	}

	protected function defaults(): array {
		return [
			self::TAG                => 'article',
			self::CLASSES            => [],
			self::ATTRS              => [],
			self::MEDIA_CLASSES      => [],
			self::CONTENT_CLASSES    => [],
			self::STYLE              => self::STYLE_PLAIN,
			self::USE_TARGET_LINK    => false,
			self::IMAGE              => null,
			self::AUTHOR             => null,
			self::DURATION           => null,
			self::META_SECONDARY     => null,
			self::TITLE              => null,
			self::DESCRIPTION        => null,
			self::CTA                => null,
			self::POST_ID            => 0,
			self::POST_TYPE_NAME     => null,
			self::MAIN_CATEGORY      => null,
			self::QUERY_TYPE         => '',
			self::HIDE_TOPIC         => false,
			self::HIDE_AUTHOR        => false,
			self::HIDE_AUTHORS_IMAGE => false,
			self::HIDE_META          => false,
			self::HIDE_MEMBERS_ONLY  => false,
			self::TOP_MEMBERS_ONLY   => false,
			self::PRICE_REGULAR      => '',
			self::PRICE_DISCOUNTED   => '',
			self::DEAL_TAG           => '',
			self::CARD_NOTE          => '',
			self::PRODUCT_TAG        => null,
		];
	}

	protected function required(): array {
		return [
			self::CLASSES         => [ 'c-card' ],
			self::MEDIA_CLASSES   => [ 'c-card__media' ],
			self::CONTENT_CLASSES => [ 'c-card__content' ],
		];
	}

	public function get_tag(): string {
		return tag_escape( $this->tag );
	}

	public function get_classes(): string {
		if ( $this->use_target_link ) {
			$this->classes[] = 'has-target-link';
		}

		if ( $this->style !== self::STYLE_PLAIN ) {
			$this->classes[] = 'c-card--style-' . $this->style;
		}

		if ( $this->get_memberonly_status() ) {
			$this->classes[] = 'members-only';
		}

		if ( $this->get_query_type() ) {
			$this->classes[] = 'c-card--query-type-' . $this->query_type;
		}

		return Markup_Utils::class_attribute( $this->classes );
	}

	public function get_attrs(): string {
		if ( $this->use_target_link ) {
			$this->attrs['data-js'] = 'use-target-link';
		}

		return Markup_Utils::concat_attrs( $this->attrs );
	}

	public function get_media_classes(): string {
		if ( $this->top_members_only && $this->get_memberonly_status() && ! $this->hide_members_only ) {
			$this->media_classes[] = 'c-card__media--locked';
		}

		return Markup_Utils::class_attribute( $this->media_classes );
	}

	public function get_content_classes(): string {
		return Markup_Utils::class_attribute( $this->content_classes );
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function render_image(): ?Deferred_Component {
		if ( empty( $this->image['img_id'] ) ) {
			return null;
		}

		$this->image[ Image_Controller::CLASSES ][] = 'c-card__image';

		return $this->image;
	}

	/**
	 * Check if we have secondary metadata
	 *
	 * @return bool
	 */
	public function has_meta_secondary(): bool {
		return ! empty( $this->meta_secondary );
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function render_meta_secondary(): ?Deferred_Component {
		if ( empty( $this->meta_secondary ) ) {
			return null;
		}

		$this->meta_secondary[ Container_Controller::CLASSES ][] = 'c-card__meta';
		$this->meta_secondary[ Container_Controller::CLASSES ][] = 'c-card__meta--secondary';

		return $this->meta_secondary;
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function render_title(): ?Deferred_Component {
		if ( empty( $this->title ) ) {
			return null;
		}

		$this->title[ Text_Controller::CLASSES ][] = 'c-card__title';

		return $this->title;
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function render_description(): ?Deferred_Component {
		if ( empty( $this->description ) ) {
			return null;
		}

		$this->description[ Container_Controller::CLASSES ][] = 'c-card__description';

		return $this->description;
	}

	public function get_cta_args(): array {
		if ( empty( $this->cta['url'] ) ) {
			return [];
		}

		$this->cta[ Link_Controller::CLASSES ][] = 'c-card__cta-link';

		return [
			Container_Controller::TAG     => 'p',
			Container_Controller::CLASSES => [ 'c-card__cta' ],
			Container_Controller::CONTENT => $this->cta->render(),
		];
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function render_author(): ?Deferred_Component {
		if ( empty( $this->author ) ) {
			return null;
		}

		// A hack to check if this is a Member Stream or Download CPT OR if "Members Only" note is hidden
		if ( $this->get_memberonly_status() && $this->hide_members_only_note() ) {
			return null;
		}

		$this->author[ Container_Controller::CLASSES ][] = 'c-card__author';
		$this->author[ Author_Controller::SHOW_AVATAR ]  = ! $this->hide_authors_image;

		return $this->author;
	}

	public function render_multiple_authors(): string {
		$author_id        = (int) get_post_field( 'post_author', $this->post_id );
		$author_name      = Author_Controller::get_author_display_name( $author_id );
		$author_link      = get_author_posts_url( $author_id );
		$author_link_html = '';
		$avatars          = '';
		$result           = '';

		if ( $author_id ) {
			if ( ! $this->hide_authors_image ) {
				$avatars .= defer_template_part( 'components/image/image', null, [
					Image_Controller::IMG_URL      => esc_url( Author_Controller::get_author_avatar_from_id( $author_id ) ),
					Image_Controller::LINK_URL     => esc_url( $author_link ),
					Image_Controller::LINK_ID      => 'item-single__author-image',
					Image_Controller::AS_BG        => false,
					Image_Controller::USE_LAZYLOAD => true,
					Image_Controller::CLASSES      => [ 'item-single__author-image' ],
					Image_Controller::IMG_ALT_TEXT => esc_attr( $author_name ),
					Image_Controller::IMG_CLASSES  => [ 'item-single__author-image-img' ],
				] );
			}

			$author_link_html = $this->get_author_link_html_tag( $author_name, $author_link );
		}

		foreach ( $this->multi_authors as $index => $id ) {
			$name = Author_Controller::get_author_display_name( $id );
			$link = get_author_posts_url( $id );

			if ( ! $this->hide_authors_image ) {
				$avatars .= defer_template_part( 'components/image/image', null, [
					Image_Controller::IMG_URL      => esc_url( Author_Controller::get_author_avatar_from_id( $id ) ),
					Image_Controller::LINK_URL     => esc_url( $link ),
					Image_Controller::LINK_ID      => 'item-single__author-image',
					Image_Controller::AS_BG        => false,
					Image_Controller::USE_LAZYLOAD => true,
					Image_Controller::CLASSES      => [ 'item-single__author-image' ],
					Image_Controller::IMG_ALT_TEXT => esc_attr( $name ),
					Image_Controller::IMG_CLASSES  => [ 'item-single__author-image-img' ],
				] );
			}

			$author_link_html .= $index === count( $this->multi_authors ) - 1 ? ' & ' : ' , ';
			$author_link_html .= $this->get_author_link_html_tag( $name, $link );
		}

		if ( ! empty( $avatars ) ) {
			$result .= sprintf(
				'<div class="item-single__avatars_wrapper">%s</div>',
				$avatars
			);
		}

		if ( $author_link_html ) {
			$result .= sprintf(
				'<div class="item-single__author-infos"><div class="item-single__author-name">By %s</div></div>',
				$author_link_html
			);
		}

		return $result;
	}

	private function get_author_link_html_tag( string $name, string $link ) {
		return sprintf(
			'<a class="item-single__author-name-link" href="%s" title="%s">%s</a>',
			esc_url( $link ),
			esc_attr( $name ),
			esc_html( $name )
		);
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function render_duration(): ?Deferred_Component {
		if ( empty( $this->duration ) ) {
			return null;
		}

		$this->duration[ Container_Controller::CLASSES ][] = 'c-card__duration';
		$this->duration[ Container_Controller::TAG ]       = 'span';

		return $this->duration;
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function render_post_type_name(): ?Deferred_Component {
		if ( empty( $this->post_type_name ) ) {
			return null;
		}

		$this->post_type_name[ Container_Controller::CLASSES ][] = 'c-card__post-type';
		$this->post_type_name[ Container_Controller::TAG ]       = 'span';

		return $this->post_type_name;
	}

	/**
	 * @return array
	 */
	public function get_main_category(): array {
		if ( empty( $this->main_category['url'] ) || ! isset( $this->main_category['content'] ) || ! $this->main_category['content'] || $this->hide_topic() ) {
			return [];
		}

		$this->main_category[ Link_Controller::CLASSES ][] = 'c-card__cta-link';

		return [
			Container_Controller::TAG     => 'span',
			Container_Controller::CLASSES => [ 'c-card__category-link' ],
			Container_Controller::CONTENT => $this->main_category->render(),
		];
	}

	public function get_query_type() {
		return $this->query_type;
	}

	public function hide_topic() {
		return $this->hide_topic;
	}

	public function hide_author() {
		return $this->hide_author;
	}

	public function hide_authors_image() {
		return $this->hide_authors_image;
	}

	public function hide_meta() {
		return $this->hide_meta;
	}

	public function hide_reading_time() {
		return $this->hide_reading_time;
	}

	/**
	 * Is this a Download Post?
	 *
	 * @return bool
	 */
	public function is_download_post(): bool {
		return get_post_type( $this->post_id ) === Download::NAME;
	}

	/**
	 * Get download file name and type
	 *
	 * @return array
	 */
	public function get_downloadable_file_types(): array {
		$files  = get_field( Download_Meta::FILES, $this->post_id );
		$return = [];

		if ( $files ) {
			foreach ( $files as $file ) {
				$return[] = [
					'file_name' => esc_html( $file[ Download_Meta::DISPLAY_NAME ] ),
					'file_type' => esc_html( $file[ Download_Meta::FILE_TYPE ] ),
				];
			}
		}

		return $return;
	}

	public function hide_members_only_note() {
		return $this->hide_members_only;
	}

	/**
	 * Is this an upcoming Member Stream event?
	 *
	 * @return bool
	 */
	public function is_upcoming_event(): bool {
		if ( get_post_type( $this->post_id ) !== Member_Stream::NAME ) {
			return false;
		}

		$post_state = get_the_terms( $this->post_id, Post_State::NAME );

		if ( ! $post_state || is_wp_error( $post_state ) ) {
			return false;
		}

		return $post_state[0]->slug === Post_State::UPCOMING;
	}

	private function format_event_date( string $date, string $format = 'c' ): ?string {
		try {
			$formattedDate = ( new DateTime( $date, wp_timezone() ) )->format( $format );

			return $formattedDate;
		} catch ( \Exception $e ) {
			return null;
		}
	}

	/**
	 * Get the raw date and time of an upcoming Member Stream
	 *
	 * @return string|null
	 */
	public function get_event_date_raw(): ?string {
		$date = get_field( Member_Stream_Meta::DATE, $this->post_id );
		$time = get_field( Member_Stream_Meta::TIME, $this->post_id );

		if ( ! $date ) {
			return null;
		}

		return $this->format_event_date( $date . ( $time ? ' ' . $time : '' ), 'c' );
	}

	/**
	 * Get the date of an upcoming Member Stream
	 *
	 * @return string|null
	 */
	public function get_event_date(): ?string {
		$date = get_field( Member_Stream_Meta::DATE, $this->post_id );

		if ( ! $date ) {
			return null;
		}

		return $this->format_event_date( $date, 'l, F jS' );
	}

	/**
	 * Get the time of an upcoming Member Stream
	 *
	 * @return string|null
	 */
	public function get_event_time(): ?string {
		$time = get_field( Member_Stream_Meta::TIME, $this->post_id ) ?? null;

		if ( ! $time ) {
			return null;
		}

		return $this->format_event_date( $time, 'h:i a T' );
	}

	public function get_upcoming_event_link(): array {
		if ( empty( $this->cta['url'] ) ) {
			return [];
		}

		return [
			Link_Controller::URL     => $this->cta['url'],
			Link_Controller::CLASSES => [ 'c-card__registration-link', 'a-cta' ],
			Link_Controller::CONTENT => __( 'Register Now', 'tribe' ),
		];
	}

	/**
	 * Used to add a Member's Only tag to a card for Downloads and Member Stream CPTs
	 *
	 * @return bool
	 */
	public function get_memberonly_status(): bool {
		return ! $this->is_user_authorized_to_see( $this->post_id );
	}


	public function is_dashboard(): bool {
		return is_page( 'dashboard' );
	}

	public function get_separator_element(): string {
		/* When we activate the "Hide Topic" option main category content returns empty
		and the seperator class should be empty too on this scenario */
		if ( ! isset( $this->main_category['content'] ) || ! $this->main_category['content'] ) {
			return '';
		}

		if ( ! empty( $this->render_duration()['content'] ) ) {
			return '<span class="c-card__sep-dot"></span>';
		}

		return '';
	}

	/**
	 * Pass through the post ID to the badge component
	 *
	 * @return array
	 */
	public function get_watched_badge(): array {
		if ( empty( $this->post_id ) ) {
			return [];
		}

		return [
			Badges_Controller::POST_ID => $this->post_id,
		];
	}

	public function is_multi_authors(): bool {
		return count( $this->multi_authors ) > 0;
	}

	public function get_price_regular(): ?Deferred_Component {
		if ( empty( $this->price_discounted ) ) {
			return null;
		}

		return defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'p',
			Text_Controller::CLASSES => [ 'c-card__price__regular' ],
			Text_Controller::CONTENT => $this->price_regular ?? '',
		] );
	}

	public function get_price_discounted(): ?Deferred_Component {
		if ( empty( $this->price_discounted ) ) {
			$price = $this->price_regular;
		} else {
			$price = $this->price_discounted;
		}

		if ( ! $price ) {
			return null;
		}

		return defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'p',
			Text_Controller::CLASSES => [ 'c-card__price__discounted' ],
			Text_Controller::CONTENT => $price,
		] );
	}

	public function get_deal_tag(): ?Deferred_Component {
		return defer_template_part( 'components/container/container', null, [
			Container_Controller::TAG     => 'p',
			Container_Controller::CLASSES => [ 'c-card__price__deal-tag' ],
			Container_Controller::CONTENT => $this->deal_tag ?? '',
		] );
	}

	public function get_card_note(): string {
		if ( ! $this->card_note ) {
			return '';
		}

		return $this->card_note;
	}

	public function get_product_tag(): ?Deferred_Component {
		if ( ! $this->product_tag ) {
			return null;
		}

		return $this->product_tag;
	}

	public function has_members_only_on_top(): bool {
		return $this->top_members_only;
	}
}
