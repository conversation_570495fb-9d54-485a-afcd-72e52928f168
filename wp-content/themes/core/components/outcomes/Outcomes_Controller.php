<?php

namespace Tribe\Project\Templates\Components\outcomes;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Object_Meta\Member_Stream_Meta;

/**
 * Class Outcomes
 */
class Outcomes_Controller extends Abstract_Controller {
	public const TAG            = 'tag';
	public const CLASSES        = 'classes';
	public const ATTRS          = 'attrs';

	private string $tag;
	private array  $classes;
	private array  $attrs;
	private string $title;
	private array  $list;

	public function __construct( array $args = [] ) {
		$args = $this->parse_args( $args );

		$this->tag           = (string) $args[ self::TAG ];
		$this->classes       = (array) $args[ self::CLASSES ];
		$this->attrs         = (array) $args[ self::ATTRS ];
		$this->title 	     = (string) $args[ Member_Stream_Meta::OUTCOMES_NAME ];
		$this->list          = (array) $args[ Member_Stream_Meta::OUTCOMES_LIST ];
	}

	protected function defaults(): array {
		return [
			self::TAG           => 'section',
			self::CLASSES       => [],
			self::ATTRS         => [],
		];
	}

	protected function required(): array {
		return [
			self::CLASSES => [ 'c-outcomes', 's-sink' ],
		];
	}

	public function get_tag(): string {
		return tag_escape( $this->tag );
	}

	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	public function get_attrs(): string {
		return Markup_Utils::concat_attrs( $this->attrs );
	}

	public function get_title_classes(): string {
		return Markup_Utils::class_attribute( [ 'c-outcomes__title', 'h6' ] );
	}

	public function get_title(): string {
		if ( empty( $this->title ) ) {
			return '';
		}

		return esc_html( $this->title );
	}

	public function has_list(): bool {
		return ! empty( $this->list[0] );
	}

	public function get_list(): array {
		return $this->list;
	}

	public function get_list_item( $item ): string {
		return esc_html( $item[ Member_Stream_Meta::OUTCOMES_ITEM ] );
	}
}
