<?php
declare( strict_types=1 );

use Tribe\Project\Templates\Components\outcomes\Outcomes_Controller;

/**
 * @var array $args Arguments passed to the template
 */
// phpcs:ignore VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable
$c = Outcomes_Controller::factory( $args );
?>

<<?php echo $c->get_tag(); ?>
	<?php echo $c->get_classes(); ?>
	<?php echo $c->get_attrs(); ?>
>
	<div class="c-outcomes__contain">
		<?php if ( ! empty( $c->get_title() ) ) : ?>
			<h3 <?php echo $c->get_title_classes(); ?>>
				<?php echo $c->get_title(); ?>
			</h3>
		<?php endif; ?>

		<?php if ( $c->has_list() ) : ?>
			<ul>
				<?php foreach ( $c->get_list() as $key => $item ) : ?>
					<?php if ( $item ) : ?>
						<li>
							<?php echo $c->get_list_item( $item ); ?>
						</li>
					<?php endif; ?>
				<?php endforeach; ?>
			</ul>
		<?php endif; ?>
	</div>

</<?php echo $c->get_tag(); ?>>
