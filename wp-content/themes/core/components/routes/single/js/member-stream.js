/**
 * @module Member Stream Event Time
 * @description Controls the users local time for the Member Stream upcoming event
 */

import * as tools from 'utils/tools';
import { on } from 'utils/events';
import { convertToLocalTimezone, timezoneFormat, timezoneLocale } from 'utils/data/local-timezone';

const el = {
	eventDateContainers: tools.getNodes( '.event-date-container', true, document, true ),
	memberCards: tools.getNodes( '.item-single__stream-speakers .item-single__author-container', true, document, true ),
	memberBioTogglers: tools.getNodes( '.item-single__stream-speakers .item-single__author-actions__see-more a', true, document, true ),
	eventContent: tools.getNodes( '.item-single__stream-details .item-single__content', true, document, true )[ 0 ],
};

const expandableClass = 'item-single__author-container--expandable';
const expandedClass = 'item-single__author-container--expanded';

/**
 * @param card
 * @param button
 * @description Expand the Author Biography
 */
const showAuthorBio = ( card, button ) => {
	tools.addClass( card, expandedClass );
	button.innerText = button.getAttribute( 'data-expanded-text' );
};

/**
 * @param card
 * @param button
 * @description Collapse the Author Biography
 */
const hideAuthorBio = ( card, button ) => {
	tools.removeClass( card, expandedClass );
	button.innerText = button.getAttribute( 'title' );
};

/**
 * @description Show or Hide the "See More" link in Host and Guest Cards
 */
const handleSpeakerSeeMoreButton = () => {
	if ( 0 === el.memberCards.length ) {
		return;
	}

	el.memberCards.forEach( ( card ) => {
		let authorBio     = tools.getNodes( '.item-single__author-about', false, card, true );
		let seeMoreButton = tools.getNodes( '.item-single__author-actions__see-more a', false, card, true );

		// In some cases, the host/guest doesn't have a bio
		if ( 1 === authorBio.length && 1 === seeMoreButton.length ) {
			authorBio = authorBio[ 0 ];
			seeMoreButton = seeMoreButton[ 0 ];

			if ( authorBio.scrollHeight > authorBio.offsetHeight ) {
				if ( ! tools.hasClass( card, expandableClass ) ) {
					tools.addClass( card, expandableClass );
				}
			} else if ( tools.hasClass( card, expandableClass ) ) {
				tools.removeClass( card, expandableClass );
				hideAuthorBio( card, seeMoreButton );
			}
		}
	} );
};

/**
 * @description Append the "See More" button in large content descriptions
 */
const handleContentSeeMoreButton = () => {
	if ( ! el.eventContent || 4 >= el.eventContent.childElementCount ) {
		return;
	}

	const contentHiddenClass  = 'item-single__content--hidden';
	const contentVisibleClass = 'item-single__content--visible';

	const seeMore = document.createElement( 'button' );
	seeMore.innerText = 'Show more';
	seeMore.classList.add( 'a-btn-secondary', 'item-single__stream-show-details' );

	el.eventContent.classList.add( contentHiddenClass );
	el.eventContent.appendChild( seeMore );

	on( seeMore, 'click', function() {
		el.eventContent.classList.remove( contentHiddenClass );
		el.eventContent.classList.add( contentVisibleClass );

		seeMore.remove();
	} );
};

/**
 * @function toggleAuthorBio
 * @param event
 * @description Collapse or Expand the Author Biography in Host and Guest Cards
 */
const toggleAuthorBio = ( event ) => {
	event.preventDefault();

	const card = event.target.parentNode.parentNode.parentNode;

	if ( ! tools.hasClass( card, expandedClass ) ) {
		showAuthorBio( card, event.target );
	} else {
		hideAuthorBio( card, event.target );
	}
};

/**
 * @function bindEvents
 * @description Bind the events for this module.
 */

const bindEvents = () => {
	on( window, 'resize', handleSpeakerSeeMoreButton );

	if ( 0 !== el.memberBioTogglers.length ) {
		el.memberBioTogglers.forEach( ( button ) => {
			on( button, 'click', toggleAuthorBio );
		} );
	}
};

/**
 * @function updateEventDateLocale
 * @description
 */

const updateEventDateLocale = () => {
	if ( 0 === el.eventDateContainers.length ) {
		return;
	}

	el.eventDateContainers.forEach( ( container ) => {
		const eventDay = tools.getNodes( '.event-date-day', true, container, true );
		const eventTime = tools.getNodes( '.event-date-time', true, container, true );

		if ( 1 === eventDay.length && 1 === eventTime.length && container.dataset.date ) {
			eventDay[ 0 ].innerHTML = convertToLocalTimezone( container.dataset.date, timezoneFormat.WEEKDAY_MONTH_DAY, timezoneLocale.EN );
			eventTime[ 0 ].innerHTML = convertToLocalTimezone( container.dataset.date, timezoneFormat.TIME_AM_PM, timezoneLocale.EN );
		}
	} );
};

const init = () => {
	updateEventDateLocale();
	handleSpeakerSeeMoreButton();
	handleContentSeeMoreButton();
	bindEvents();

	console.info( 'SquareOne Theme: Initialized Member Stream Single and Event Dates' );
};

export default init;
