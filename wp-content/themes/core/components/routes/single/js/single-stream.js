/** -----------------------------------------------------------------------------
 *
 * Route: Single Stream post
 *
 * ----------------------------------------------------------------------------- */

import * as tools from 'utils/tools';
import { on } from 'utils/events';

const el = {
	show_more: tools.getNodes( '.item-single__stream-details__show-more button', true, document, true )[ 0 ],
};

/**
 * @function handleShowMore
 * @description Handles the click event on the load more button
 */
const handleShowMore = () => {
	tools.getNodes( '.item-single__stream-details', true, document, true )[ 0 ].classList.toggle( 'item-single__stream-details--expanded' );

	const attr = 'data-toggle-text';
	const textToShow = el.show_more.getAttribute( attr );

	el.show_more.setAttribute( attr, el.show_more.innerHTML );
	el.show_more.innerHTML = textToShow;
};

/**
 * @function bindEvents
 * @description Bind the events for this module here.
 */
const bindEvents = () => {
	on( el.show_more, 'click', handleShowMore );
};

const init = () => {
	if ( ! el.show_more ) {
		return;
	}

	bindEvents();

	console.info( 'SquareOne Theme: Initialized Single Stream scripts.' );
};

export default init;
