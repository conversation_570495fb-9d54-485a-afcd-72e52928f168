/* -----------------------------------------------------------------------------
 *
 * Route: Single article post
 *
 * This file is just a clearing-house, see the js directory
 * and edit the source files found there.
 *
 * ----------------------------------------------------------------------------- */

import singlePost from './js/single';
import singlePerfectPost from './js/single-perfect-post-layout';
import singlePerfectListicle from './js/single-perfect-listicle-layout';
import singleInDepthReviews from './js/single-in-depth-reviews';
import singleXvsY from './js/single-xvsy';
import singleAlternativesTo from './js/single-alternatives-to';
import singlePerfectServiceListicle from './js/single-perfect-service-listicle-layout';
import singleStream from './js/single-stream';
import editorial from './js/editorial';
import downloadPost from './js/download';
import memberStreamPost from './js/member-stream';
import videoWatched from './js/video-watched';

const init = () => {
	singlePost();
	singlePerfectPost();
	singlePerfectListicle();
	singleInDepthReviews();
	singleXvsY();
	singleAlternativesTo();
	singlePerfectServiceListicle();
	singleStream();
	editorial();
	downloadPost();
	memberStreamPost();
	videoWatched();
};

export default init;
