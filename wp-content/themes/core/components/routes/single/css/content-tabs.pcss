.item-single__tabs-wrapper {
	display: flex;
    align-items: baseline;
    justify-content: space-between;

	@media (--viewport-full-max) {
		flex-direction: column-reverse;
    	gap: var(--spacer-40);
	}
}

.item-single__content-tabs {
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 calc(-1 * var(--grid-margin-small));

	@media (--viewport-medium) {
		margin: 0;
	}

	.item-single--media & {
		margin-bottom: var(--spacer-30);
	}
}

.item-single__content-tabs-button {
	@mixin a-btn-reset;
	@mixin t-display-xxx-small;
	font-family: var(--font-family-base);
	font-weight: var(--font-weight-semibold);
	font-size: var(--font-size-body-small);
	letter-spacing: 0;

	padding-bottom: var(--spacer-30);
	border-bottom: 6px solid transparent;
	transition: var(--transition);
	margin: 0 var(--spacer-20);

	&:first-of-type {
		margin-left: 0;
	}

	&:hover,
	&:focus {
		color: var(--color-primary);
	}

	&[aria-selected="true"],
	&:focus-visible {
		border-bottom-color: var(--color-primary);
		color: var(--color-primary);
	}

	&--disabled {
		color: var(--color-neutral-30);
		pointer-events: none;
    	cursor: default;
	}
}

.item-single__tab-content {

	&[hidden="false"] {
		display: block;
	}
}
