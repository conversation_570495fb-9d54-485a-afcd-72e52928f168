.item-single--stream {
	overflow: hidden;
	margin: var(--spacer-40) 0;

	body.is-mobile:has(.membership-navigation) & {
		margin-top: var(--spacer-80);
	}

	body.admin-bar & {
		padding-top: 40px;
	}

	.item-single__tab-content {
		padding-block: var(--spacer-60);
		position: relative;

		&:has(.item-single__stream-details__show-more) {
			margin-bottom: var(--spacer-40);
		}
	}

	.item-single__content-wrapper {
		padding-bottom: 0;
	}

	.item-single__stream-details__header .item-single__category-meta {
		margin-bottom: var(--spacer-20);
	}

	.item-single__transcript-container {
		margin: 0;
		padding: 0;

		h3 {
			font-weight: var(--font-weight-semibold);
		}
	}

	&--hidden {
		position: relative;

		&:after {
			content: '';
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			height: calc( var(--spacer-80) * 2 );
			z-index: 1;
			background: var(--color-white);
			background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, var(--color-white) 70%);
		}

		.item-single__stream-show-details {
			display: block;
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			bottom: 0;
			z-index: 2;
		}
	}

	.page-title {
		font-weight: var(--font-weight-semibold);
		margin-bottom: var(--spacer-20);
	}

	@media (--viewport-medium-max) {
		.item-single__date {
			margin-bottom: var(--spacer-20);
		}
	}

	.c-post-info {
		margin-bottom: 0;

		justify-content: flex-start;

		@media (--viewport-medium-max) {
			align-items: center;
		}

		&.layout--one-line .c-post-info__author-container {
			font-size: var(--font-size-body-small);

			@media (--viewport-medium-max) {
				text-align: left;
			}
		}

		&.layout--one-line .c-post-info__author-name {
			font-size: var(--font-size-body-small);
    		font-weight: var(--font-weight-regular);
		}

		.item-single__author-image {
			flex-shrink: 0;

			&:not(:first-child) {
				margin-left: -15px;
			}
		}
	}

	.item-single {
		&__category-meta {
			margin-bottom: var(--spacer-40);
		}

		&__share {
			margin-top: 0;

			&-title {
				display: none;
			}

			.watched-button {
				&:not(:only-child) {
					margin-right: var(--spacer-20);
				}

				.watched-button__icon {
					background-color: var(--color-white);
					background-image: none;
					border-radius: 0;
					mask-image: url(/wp-content/themes/core/assets/img/icons/eye.svg);
					mask-repeat: no-repeat;
					mask-size: 100%;
					width: 18px;
					mask-position: center center;
				}

				&:not(.watched):hover .watched-button__icon,
				&:not(.watched):active .watched-button__icon,
				&:not(.watched):focus .watched-button__icon {
					background-color: var(--color-white);
				}

				&:not(.watched) .watched-button__icon {
					background-color: var(--color-primary);
				}
			}
		}

		&__video-container,
		&__video-placeholder,
		&__featured-image {
			margin-bottom: var(--spacer-30);
		}

		&__tabs-wrapper {
			position: relative;

			&:before {
				content: "";
				position: absolute;
				bottom: 4px;
				left: -100vw;
				width: 200vw;
				height: 500vh;
				background-color: var(--color-neutral-10);
				z-index: -1;
			}

			&:not(:has(.watched-button)) {
				.social-share {
					ul {
						padding-left: 0;
					}
				}
			}
		}

		&__membership-perks {
			margin: 0;
			padding: 0;

			&__title {
				font-weight: var(--font-weight-semibold);
				margin-bottom: var(--spacer-40);
			}

			&__list {
				margin: 0;
				padding: 0;

				&__item {
					@mixin t-body-small;

					display: grid;
					grid-template-columns: 20px 1fr;
					align-items: flex-start;
					padding: 4px 0;
					column-gap: var(--spacer-20);

					&:before {
						@mixin icon;

						content: var(--icon-check);
						color: var(--color-primary);
						font-size: 28px;
						width: 20px;
						display: flex;
						justify-content: center;
					}
				}
			}

			.a-btn {
				margin-top: var(--spacer-40);
			}
		}

		&__video-placeholder {
			@media (--viewport-medium-max) {
				margin-left: calc(var(--spacer-30) * -1);
    			margin-right: calc(var(--spacer-30) * -1);
			}

			.c-image {
				position: relative;

				&:before {
					@mixin p-fill;

					content: "";
					background-color: rgba(0, 12, 42, 0.5);
					border-radius: var(--border-radius-media);
					border-bottom-right-radius: 0;
    				border-bottom-left-radius: 0;

					@media (--viewport-medium-max) {
						border-radius: 0;
					}
				}

				img {
					border-bottom-right-radius: 0;
    				border-bottom-left-radius: 0;

					@media (--viewport-medium-max) {
						border-radius: 0;
					}
				}
			}

			&-overlay {
				position: relative;
				align-items: flex-end;
				overflow: hidden;
				border-radius: var(--border-radius-media);
				border-top-right-radius: 0;
    			border-top-left-radius: 0;

				@media (--viewport-medium-max) {
					border-radius: 0;
				}

				.item-single {
					&__restricted-content {
						width: 100%;
						padding: var(--spacer-30) var(--spacer-50);
						background-color: var(--color-background-dark);
						cursor: default;

						&__message-container {
							display: grid;
							grid-template-columns: 13px 1fr;
							column-gap: var(--spacer-10);
							align-items: center;

							svg {
								max-width: 100%;

								path {
									fill: var(--color-alert-negative);
								}
							}
						}

						&__message {
							@mixin t-overline;

							color: var(--color-alert-negative);
							font-weight: var(--font-weight-semibold);
						}

						&__description {
							@mixin t-body-small;

							color: var(--color-white);
							margin-top: var(--spacer-10);
						}
					}
				}
			}
		}

		&__resources {
			display: inline-block;

			&__file-item {
				@mixin a-btn-global;

				float: left;
				display: grid;
				grid-template-columns: calc(var(--spacer-30) - 3px) 1fr;
				column-gap: calc(var(--spacer-30) - 2px);
				padding: 0;
				width: 100%;
				color: var(--color-primary);
				text-align: left;
				margin-bottom: var(--spacer-30);
				text-transform: none;

				&::before {
					content: '';
					display: block;
					background-color: var(--color-primary);
					border-radius: 0;
					mask-image: var(--icon);
					mask-repeat: no-repeat;
					mask-size: auto 100%;
					mask-position: center center;
				}

				&:hover,
				&:focus {
					span {
						text-decoration: underline;
					}
				}

				&:last-child {
					margin-bottom: 0;
				}

				.c-image {
					margin-top: 0;
				}

				&__img--lock {
					img {
						max-width: 19px;
					}
				}

				&--disabled {
					pointer-events: none;
					color: var(--color-neutral-30);

					&::before {
						background-color: var(--color-neutral-30);
					}
				}
			}
		}

		&__related_courses {
			display: inline-block;

			&:not(:only-child) {
				margin-top: var(--spacer-50);
    			padding-top: var(--spacer-50);
				border-top: solid 1px var(--color-neutral-20);
			}

			img {
				border-radius: var(--border-radius-media);
				max-width: 120px;
			}

			&__cta {
				display: flex;
    			gap: var(--spacer-30);

				&:not(:last-child) {
					margin-bottom: var(--spacer-40);
				}

				&:hover,
				&:focus {
					.item-single__related_courses__title {
						text-decoration: underline;
					}
				}
			}

			&__members-only-warning {
				@mixin t-label;

				font-weight: var(--font-weight-bold);
				font-size: var(--font-size-body-xxsmall);
				display: grid;
				align-items: baseline;
				grid-template-columns: 5px 1fr;
				column-gap: var(--spacer-10);
				margin-bottom: var(--spacer-10);

				.c-card--query-type-manual & {
					display: none;
				}

				&:before {
					content: "";
					display: inline-block;
					width: 5px;
					height: 5px;
					border-radius: 50%;
					background-color: var(--color-primary);
					margin-right: var(--spacer-10);
					transform: translateY(-2px);
				}
			}
		}

		&__login-wrapper {
			margin-top: var(--spacer-50);
		}

		&__subscribe-form {
			&__title,
			&__description {
				margin-bottom: var(--spacer-30);
			}

			&__title {
				line-height: 1.5;
			}
		}

		&__sub_title {
			font-weight: var(--font-weight-semibold);
			margin-bottom: var(--spacer-30);
		}

		&__custom-sidebar_message {
			font-weight: var(--font-weight-semibold);
		}
	}

	.item-single__stream-details {		
		.page-title {
			text-align: left;
		}

		.t-overline {
			margin-bottom: var(--spacer-30);
			display: block;
		}

		&__sponsor {
			.t-overline {
				margin-bottom: var(--spacer-20);
				line-height: 1;
				color: var(--color-neutral-50);
				font-weight: var(--font-weight-semibold);

				@media (--viewport-medium-max) {
					margin-bottom: 0;
				}
			}

			.c-image img {
				max-height: 60px;
				max-width: 50%;
				object-fit: contain;
				object-position: left;

				@media (--viewport-medium-max) {
					max-width: 50vw;
				}
			}

			@media (--viewport-medium-max) {
				display: flex;
				flex-wrap: wrap;
				align-items: center;
				gap: var(--spacer-20);
				border-top: solid 1px var(--color-neutral-30);
				padding-top: var(--spacer-30);
			}
		}

		.c-outcomes {
			ul {
				font-size: var(--font-size-body);
				list-style: disc outside;
				margin: var(--spacer-30) 0 var(--spacer-40);
				padding-left: calc( var(--spacer-20) + 4px );

				@media (--viewport-medium-max) {
					font-size: var(--font-size-body-small);
				}
				
				&.social-share-networks {
					margin: 0;
				}

				li ~ li {
					margin-top: 0;
				}

				li {
					&::marker {
						color: var(--color-primary);
						font-size: 14px;
					}

					&:not(:last-child) {
						margin-bottom: var(--spacer-10);
					}
				}
			}
		}

		&__hidden {
			max-height: 0;
			overflow: hidden;
			transition: all 300ms;

			&.s-sink>:last-child {
				margin-bottom: var(--spacer-40);
			}
		}

		&--expanded .item-single__stream-details__hidden {
			max-height: 1000px;
		}

		&__show-more {
			width: 100%;
			position: absolute;
			bottom: 0;
			background: rgb(255, 255, 255);
			background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 70%);
			padding: var(--spacer-90) 0 0 0;
			transition: all 300ms;
		}

		&--expanded .item-single__stream-details__show-more {
			padding: 0;
			background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0) 70%);
		}

		gap: var(--spacer-40);

		@media(--viewport-full) {
			display: grid;
			gap: var(--spacer-40) var(--spacer-70);
			grid-template-columns: 1fr var(--grid-4-col);
		}
	}

	.c-countdown {
		&:not(.initialized) {
			opacity: 0;
		}

		&:not(:first-child) {
			margin-top: var(--spacer-40);

			@media(--viewport-medium-max) {
				margin-top: var(--spacer-10);
				width: 100%;
			}
		}

		@media(--viewport-full) {
			display: flex;
			align-items: flex-end;
		}

		&__title {
			line-height: 1;
		}

		&__format {
			margin-top: 0;
		}

		&__timer-container {
			width: 100%;
			box-shadow: var(--box-shadow-20);
			padding: var(--spacer-20);
		}
	}

	.item-single__stream-details__aside {
		position: relative;

		&__content-wrapper {
			background-color: var(--color-white);
			border-radius: var(--border-radius-media);
			box-shadow: var(--box-shadow-20);
			padding: var(--spacer-50) var(--spacer-40);

			&:not(:first-child) {
				margin-top: var(--spacer-40);
			}

			.gform_wrapper {
				width: 100%;
			}

			.gform_confirmation_wrapper {
				margin: 0;
				padding: var(--spacer-20);
				padding-bottom: var(--spacer-40);

				.gform_confirmation_message {
					padding-top: 0;
					font-size: var(--font-size-body-small);
				}
			}
		}
	}

	.item-single__stream-speakers {
		margin-bottom: var(--spacer-60);
		width: 100%;
		display: grid;
		gap: var(--spacer-60);
		align-items: start;
		margin-top: var(--spacer-40);
		border-radius: var(--border-radius-media);

		@media (--viewport-medium) {
			& {
				grid-template-columns: repeat(2, 1fr);
			}
		}

		@media (--viewport-large) {
			&--third-columns {
				grid-template-columns: repeat(3, 1fr);
			}
		}

		.item-single__author-type {
			@mixin t-overline;

			margin-bottom: var(--spacer-20);
		}

		.item-single__author-container {
			align-items: flex-start;
			flex-direction: column;
			box-shadow: var(--box-shadow-20);
			padding: var(--spacer-30);
			background-color: var(--color-white);
			margin-bottom: 0;

			&--expanded .item-single__author-about {
				overflow: initial;
				display: initial;
				-webkit-box-orient: initial;
				-webkit-line-clamp: initial;
			}

			&--expandable .item-single__author-actions__see-more {
				display: block;
			}
		}

		.item-single__author-image {
			max-width: 100%;
			flex: 0 0 70px;
			width: 70px;
			height: 70px;
			margin-right: 0;
			display: block;
		}

		.item-single__author-infos {
			display: flex;
			gap: var(--spacer-20);
			width: 100%;
			flex: 0;
			margin-bottom: var(--spacer-20);
		}

		.item-single__author-name {
			&:not(:last-child) {
				margin-bottom: var(--spacer-10);
			}
		}

		.item-single__author-job-title {
			@mixin t-label;
		}

		.item-single__author-about {
			@mixin t-body-small;
			overflow: hidden;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 3;
		}

		.item-single__author-links {
			flex-direction: column;
			align-items: flex-start;
			gap: 0;

			&-link {
				.icon, span {
					color: var(--color-text);
				}
			}
		}

		.item-single__author-actions {
			display: flex;
			align-items: center;
			gap: var(--spacer-20);
			margin-top: var(--spacer-20);

			&__action:first-child:not(:only-child) {
				padding-right: var(--spacer-20);
				border-right: solid 1px var(--color-grey);
			}

			&__see-more {
				display: none;
			}
		}
	}

	.item-single__stream-details__aside__content-wrapper + .item-single__tabs-wrapper,
	.item-single__share + .item-single__tabs-wrapper {
		margin-top: var(--spacer-40);
		padding-top: var(--spacer-30);
    	border-top: solid 1px var(--color-neutral-30);
	}

	.item-single__video-container {
		width: 100%;
		aspect-ratio: '640:360';
		margin-bottom: var(--spacer-30);

		@media (--viewport-medium-max) {
			margin-inline: calc(var(--spacer-30) * -1);
    		width: calc(100% + var(--spacer-30) + var(--spacer-30));
		}
	}

	.item-single__embed-container--video {
		filter: drop-shadow(0 4px 40px rgba(21, 21, 21, 0.08));
		width: 100%;
		height: 100%;
		border-radius: 0;
		
		@media (--viewport-medium-max) {
			.c-video, .c-video iframe {
				border-radius: 0;
			}
		}

		@media (--viewport-medium) {
			border-radius: var(--border-radius-media);
		}


	}
}

.item-single__stream-footer {
	background-color: var(--color-neutral-10);
	padding: var(--spacer-80) 0;

	.c-block.b-media-text {
		margin: 0;

		.b-media-text__title {
			max-width: 500px;
			font-weight: var(--font-weight-semibold);
		}
		
		.b-media-text__media {
			.c-image {
				height: 100%;

				img {
					height: 100%;
					border-radius: var(--border-radius-base);
					object-fit: cover;
				}
			}
		}

		.s-sink p {
			font-size: var(--font-size-body-small);
		}

		.b-links__list-item {
			&::before {
				height: var(--spacer-30);
			}

			p {
				font-size: var(--font-size-body-small);
			}
		}
	}
}

.item-single__stream-details__aside__content-wrapper__oauth {
	margin-top: var(--spacer-40);

	p {
		font-size: var(--font-size-body-small);
		display: flex;
		align-items: center;
		gap: var(--spacer-20);
		margin-bottom: 0;

		&:last-child {
			align-items: flex-start;
			height: var(--spacer-30);
			margin-top: var(--spacer-20);
		}

		.a-btn {
			font-size: 0;
			padding: 0;
			width: var(--spacer-40);
			height: var(--spacer-40);
			border-radius: 20%;
			outline: solid 1px var(--color-black);
			color: var(--color-black);
			background-color: transparent;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: none;

			&:hover {
				background-color: rgba( 0, 0, 0, 0.1 );
				color: var(--color-black);
			}

			&::before {
				display: block;
				font-family: var(--font-family-core-icons);
				font-size: var(--font-size-body-large);
				line-height: 0;
				font-weight: var(--font-weight-regular);
			}

			&:first-child::before {
				content: "\ea88";
				font-size: var(--font-size-body-small);
			}

			&:nth-child(2)::before {
				content: "\e917";
				font-size: calc(var(--font-size-body-large) + 5px);
				margin-bottom: 1px;
				margin-left: 1px;
			}
		}
	}		
}

.item-single__stream__modal-free-account,
.item-single__stream__modal-paid-account {
	.gform_wrapper {
		width: 100%;
	}

	.c-block.b-media-text {
		margin: 0;
	}

	.c-modal__container {
		text-align: left;
		font-size: var(--font-size-body-small);
		width: 930px;

		.b-media-text__container {
			padding: 0;
		}
	
		p, .b-links__list-item p {
			font-size: var(--font-size-body-small);
		}

		.c-block__title {
			@mixin t-overline;
			font-weight: var(--font-weight-semibold);
		}
	}

	.c-block--width-grid:not(.c-block--layout-center) .b-media-text__media {
		width: calc(40% - var(--grid-gutter-half));
	}

	.b-media-text__content .b-links .list-type--checklist .b-links__list-item:before {
		height: var(--spacer-30);
	}

	.c-block--width-grid:not(.c-block--layout-center) .b-media-text__container {
		align-items: stretch;

		.c-image { 
			height: 100%;

			img {
				height: 100%;
				object-fit: cover;
				border-radius: var(--border-radius-base);
			}
		}
	}

    .s-sink .gform_wrapper > form {
		@media (--viewport-medium) {
			display: flex;
			gap: var(--spacer-30);
			margin-bottom: 0;

			.gform_footer {
				padding: 28px 0 0 0;
			}
		}	
	}

	.item-single__stream-details__aside__content-wrapper__oauth {
		@media (--viewport-medium) {
			margin-top: 0;
		}

		p:last-child {
			height: var(--spacer-40);

			a {
				text-decoration: none;
			}
		}
	}
}