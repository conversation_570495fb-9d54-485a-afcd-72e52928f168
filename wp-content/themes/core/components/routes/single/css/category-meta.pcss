.item-single__category-meta {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: var(--spacer-20);
	justify-content: center;
	flex-direction: column;
	align-items: center;

	@mixin t-overline;

	@media (--viewport-medium) {
		flex-direction: row;
		justify-content: flex-start;
	}

	.item-single--media & {
		justify-content: center;
	}

	.item-single--tool & {
		justify-content: center;
	}

	.item-single--stream & {
		white-space: nowrap;
		flex-direction: row;
		justify-content: flex-start;

		.item-single__category-link:not(:last-child) {
			margin-right: var(--spacer-30);
			&:after {
				display: block;
			}
		}
	}

	.item-single--editorial & {
		align-items: flex-start;
	}
}

.item-single__category-link {
	color: var(--color-primary-50);
	font-weight: var(--font-weight-bold);
	position: relative;
	text-align: center;

	@media (--viewport-medium) {
		margin-right: var(--spacer-30);
	}

	/* CASE: Show separator if there is more meta data after the category link */

	&:not(:last-child) {
		&:after {
			@mixin separators-dot;
		}
	}

	/* CASE: Search page */

	.search & {
		text-align: left;
		display: inline;
		float: left;

		@media (--viewport-small-max) {
			display: flex;
			flex-direction: column;
			width: 100%;
		}

		&:has(.item-single__reading-time) {
			a {
				display: inline;

				&:after {
					@mixin separators-dot;
					margin-left: 10px;
					margin-right: 10px;
					position: inherit;

					@media (--viewport-medium) {
						display: inline-block;
					}
				}
			}
		}

		.item-single__reading-time {
			display: inline;
			color: var(--color-text);
		}
	}
}

.item-single__reading-time,
.item-single__event-type,
.item-single__secondary-meta {
	font-weight: var(--font-weight-medium);
}
