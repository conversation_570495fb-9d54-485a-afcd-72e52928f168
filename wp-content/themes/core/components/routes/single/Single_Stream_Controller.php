<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\routes\single;

use DateTime;
use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Blocks\Types\Media_Text\Media_Text;
use Tribe\Project\Integrations\Memberpress\Memberpress;
use Tribe\Project\Object_Meta\Member_Stream_Meta;
use Tribe\Project\Integrations\Memberpress\Memberpress_Meta;
use Tribe\Project\Object_Meta\Member_Stream_Settings_Meta;
use Tribe\Project\Object_Meta\User_Meta;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Post_Types\Member_Stream\Member_Stream;
use Tribe\Project\Taxonomies\Event_Type\Event_Type;
use Tribe\Project\Taxonomies\Post_State\Post_State;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\Traits\Handles_Descript_Transcripts;
use Tribe\Project\Templates\Components\Traits\Handles_MemberPress_Permissions;
use Tribe\Project\Theme\Config\Image_Sizes;
use Tribe\Project\Templates\Components\author\Author_Controller;
use Tribe\Project\Templates\Components\blocks\media_text\Media_Text_Block_Controller;
use Tribe\Project\Templates\Components\button\Button_Controller;
use Tribe\Project\Templates\Components\Traits\Page_Title;
use Tribe\Project\Templates\Components\countdown\Countdown_Controller;
use Tribe\Project\Templates\Components\Traits\Authors_Links;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\modal\Modal_Controller;
use Tribe\Project\Templates\Components\Traits\With_Get_Current_Url;

class Single_Stream_Controller extends Single_Controller {
	use Page_Title;
	use Handles_Descript_Transcripts;
	use Handles_MemberPress_Permissions;
	use Memberpress_Meta;
	use Authors_Links;
	use With_Get_Current_Url;

	public const ENABLE_MEMBERPRESS_FILTER = false;
	public const SOCIAL_LINKS_LIST = [ 'url', 'linkedin', 'twitter' ];

	private bool $on_demand;
	private bool $upcoming_event;
	private bool $public_video;
	private bool $free_account_required;
	private bool $paid_account_required;
	private bool $user_has_free_account;
	private bool $user_has_paid_account;

	private bool $has_create_paid_account_modal = false;
	private bool $has_create_free_account_modal = false;

	private ?string $subscribe_free_account_title;
	private ?string $subscribe_free_account_description;
	private ?int    $subscribe_free_account_gf_id;
	private ?int    $subscribe_free_account_popup_image;
	private ?array  $subscribe_paid_account_cta;
	private ?string $subscribe_paid_account_title;
	private ?string $subscribe_paid_account_banner_title;
	private ?string $subscribe_paid_account_description;
	private ?array  $subscribe_paid_account_benefits;
	private ?int    $subscribe_paid_account_popup_image;
	private ?int    $subscribe_paid_account_banner_image;
	private ?string $transcript;

	private ?string $description_first_part  = null;
	private ?string $description_second_part = null;

	public function __construct() {
		$content_gate = (string) $this->get_field( Member_Stream_Meta::CONTENT_GATE ) ?? Member_Stream_Meta::IS_PUBLIC_VIDEO;
		$post_state   = (string) $this->get_field( Member_Stream_Meta::STATE ) ?? Post_State::UPCOMING;

		$this->on_demand             = $post_state === Post_State::ON_DEMAND;
		$this->upcoming_event        = $post_state === Post_State::UPCOMING;
		$this->public_video          = $content_gate ? $content_gate === Member_Stream_Meta::IS_PUBLIC_VIDEO : ! $this->is_members_only();
		$this->free_account_required = $content_gate === Member_Stream_Meta::IS_FREE_ACCOUNT;
		$this->paid_account_required = $content_gate ? $content_gate === Member_Stream_Meta::IS_PAID_ACCOUNT : ! $this->public_video;
		$this->user_has_paid_account = $content_gate ? $this->current_user_has_paid_account() : $this->is_user_authorized();
		$this->user_has_free_account = $content_gate ? $this->current_user_has_free_account() : $this->user_has_paid_account;

		if ( $transcript = (array) $this->get_field( Member_Stream_Meta::TRANSCRIPT ) ) {
			$this->transcript = $this->parse_transcript( $transcript );
		} else {
			$this->transcript = null;
		}

		// Populate the subscribe with free account content
		if ( ! $this->user_has_free_account ) {
			$this->subscribe_free_account_title       = get_field( Member_Stream_Settings_Meta::SUBSCRIBE_FREE_ACCOUNT_TITLE, 'option' ) ?? __( 'Create a free account to access this video', 'tribe' );
			$this->subscribe_free_account_description = get_field( Member_Stream_Settings_Meta::SUBSCRIBE_FREE_ACCOUNT_DESCRIPTION, 'option' ) ?? __( 'Get access to exclusive content, live events and expert-curated resources.', 'tribe' );
			$this->subscribe_free_account_gf_id       = (int) get_field( Member_Stream_Settings_Meta::SUBSCRIBE_FREE_ACCOUNT_GF_ID, 'option' ) ?? null;
			$this->subscribe_free_account_popup_image = (int) get_field( Member_Stream_Settings_Meta::SUBSCRIBE_FREE_ACCOUNT_POPUP_IMAGE, 'option' ) ?? null;
		}

		// Populate the subscribe with paid account content
		if ( ! $this->user_has_paid_account ) {
			$this->subscribe_paid_account_title        = get_field( Member_Stream_Settings_Meta::SUBSCRIBE_PAID_ACCOUNT_TITLE, 'option' ) ?? sprintf( __( 'Join %s Membership to get full access', 'tribe' ), get_bloginfo( 'name' ) );
			$this->subscribe_paid_account_banner_title = get_field( Member_Stream_Settings_Meta::SUBSCRIBE_PAID_ACCOUNT_BANNER_TITLE, 'option' ) ?? __( 'Start Your Transformation', 'tribe' );
			$this->subscribe_paid_account_description  = get_field( Member_Stream_Settings_Meta::SUBSCRIBE_PAID_ACCOUNT_DESCRIPTION, 'option' ) ?? __( 'Get skilled, get confident and get connected so you can accelerate your career journey', 'tribe' );
			$this->subscribe_paid_account_popup_image  = (int) get_field( Member_Stream_Settings_Meta::SUBSCRIBE_PAID_ACCOUNT_POPUP_IMAGE, 'option' ) ?? null;
			$this->subscribe_paid_account_banner_image = (int) get_field( Member_Stream_Settings_Meta::SUBSCRIBE_PAID_ACCOUNT_BANNER_IMAGE, 'option' ) ?? null;

			if ( $benefits = get_field( Member_Stream_Settings_Meta::SUBSCRIBE_PAID_ACCOUNT_BENEFITS, 'option' ) ) {
				$this->subscribe_paid_account_benefits = array_map( function ( $list_item ) {
					return $list_item[ Member_Stream_Settings_Meta::SUBSCRIBE_PAID_ACCOUNT_BENEFITS_ITEM ];
				}, $benefits );
			}

			// Gets the paid account CTA option
			$subscribe_paid_account_cta = get_field( Member_Stream_Settings_Meta::SUBSCRIBE_PAID_ACCOUNT_CTA, 'option' );

			if ( ! $subscribe_paid_account_cta ) {
				// Gets the deprecated CTA option
				$subscribe_paid_account_cta = get_field( Member_Stream_Settings_Meta::FALLBACK_LOGGED_OUT_LINK, 'option' );

				if ( ! $subscribe_paid_account_cta ) {
					// Gets the post custom CTA
					$subscribe_paid_account_cta = $this->get_field( Member_Stream_Meta::LOGGED_OUT_LINK );
				}
			}

			if ( $subscribe_paid_account_cta ) {
				$this->subscribe_paid_account_cta = $subscribe_paid_account_cta;
			}
		}

		if ( $description = get_field( Member_Stream_Meta::DESCRIPTION, get_the_ID(), false ) ) {
			$description = apply_filters( 'the_content', preg_replace('/<!--(.|\s)*?-->/', '', $description ) );

			preg_match_all( '/<p>.*?<\/p>/is', $description, $matches, PREG_OFFSET_CAPTURE );

			$paragraphs = $matches[ 0 ];
			
			if ( count( $paragraphs ) >= 4 ) {
				$pos = (int) $paragraphs[ 3 ][ 1 ];

				$this->description_first_part  = substr( $description, 0, $pos );
				$this->description_second_part = substr( $description, $pos );
			} else {
				$this->description_first_part = $description;
			}
		}
	}

	public function is_on_demand(): bool {
		return $this->on_demand;
	}

	public function is_upcoming_event(): bool {
		return $this->upcoming_event;
	}

	public function is_public_video(): bool {
		return $this->public_video;
	}

	public function is_free_account_required(): bool {
		return $this->free_account_required;
	}

	public function is_paid_account_required(): bool {
		return $this->paid_account_required;
	}

	public function current_user_has_access(): bool {
		return ( $this->free_account_required && $this->user_has_free_account ) || ( $this->paid_account_required && $this->user_has_paid_account );
	}

	public function get_primary_category_link_args(): ?object {
		if ( $category = $this->get_primary_category() ) {
			return (object) [
				'link'  => get_term_link( $category ),
				'label' => $category->name,
			];
		}

		return null;
	}

	public function get_event_type(): string {
		$id         = get_the_ID();
		$event_type = get_the_terms( $id, Event_Type::NAME );

		if ( ! $event_type || is_wp_error( $event_type ) ) {
			return '';
		}

		return $event_type[ 0 ]->name;
	}

	public function get_title_args(): array {
		if ( empty( $this->get_page_title() ) ) {
			return [];
		}

		return [
			Text_Controller::TAG     => 'h1',
			Text_Controller::CLASSES => [ 'page-title', 'h3' ],
			Text_Controller::CONTENT => $this->get_page_title(),
		];
	}

	public function get_description_first_part(): ?string {
		if ( ! $this->description_first_part ) {
			return null;
		}

		$label = defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'span',
			Text_Controller::CLASSES => [ 't-overline' ],
			Text_Controller::CONTENT => __( 'Description', 'tribe' ),
		] );

		return $label . $this->description_first_part;
	}

	public function get_description_second_part(): ?string {
		if ( ! $this->description_second_part ) {
			return null;
		}

		$hidden_content = defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ 'item-single__stream-details__hidden', 't-sink', 's-sink' ],
			Container_Controller::CONTENT => $this->description_second_part,
		] );

		$show_more = defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ 'item-single__stream-details__show-more' ],
			Container_Controller::CONTENT => defer_template_part( 'components/button/button', null, [
				Button_Controller::ATTRS   => [ 'data-toggle-text' => 'Show Less' ],
				Button_Controller::TYPE    => 'button',
				Button_Controller::CLASSES => [ 'a-btn-secondary' ],
				Button_Controller::CONTENT => __( 'Show More', 'tribe' ),
			] ),
		] );

		return $hidden_content . $show_more;
	}

	private function format_event_date( string $date, string $format = 'c' ): ?string {
		try {
			$formattedDate = ( new DateTime( $date, wp_timezone() ) )->format( $format );

			return $formattedDate;
		} catch ( \Exception $e ) {
			return null;
		}
	}

	public function get_event_date(): ?string {
		$date = $this->get_field( Member_Stream_Meta::DATE );

		if ( ! $date ) {
			return null;
		}

		return $this->format_event_date( $date, 'l, F jS' );
	}

	public function get_event_time(): ?string {
		$time = $this->get_field( Member_Stream_Meta::TIME );

		if ( ! $time ) {
			return null;
		}

		return $this->format_event_date( $time, 'h:i a T' );
	}

	/**
	 * Get the raw event date
	 * 
	 * @param bool $post_id
	 * 
	 * @return string|null
	 */
	public function get_date_raw( $post_id = false ): ?string {
		$id   = ( $post_id === false ) ? get_the_ID() : $post_id;
		$date = get_field( Member_Stream_Meta::DATE, $id );
		$time = get_field( Member_Stream_Meta::TIME, $id );

		if ( ! $date ) {
			return null;
		}

		return $this->format_event_date( $date . ( $time ? ' ' . $time : '' ), 'c' );
	}

	public function get_host_args(): array {
		$host      = $this->get_field( Member_Stream_Meta::HOST );
		$id        = $host ? $host['ID'] : get_post_field( 'post_author', get_the_ID() );
		$job_title = (string) get_field( User_Meta::TITLE, 'user_' . $id );

		return [
			Author_Controller::AUTHOR_NAME      => Author_Controller::get_author_display_name( $id ),
			Author_Controller::AUTHOR_ID        => $id,
			Author_Controller::AVATAR_SIZE      => 70,
			Author_Controller::AUTHOR_JOB_TITLE => esc_html( $job_title ),
			Author_Controller::SHOW_DESCRIPTION => true,
			Author_Controller::SHOW_LINKS_LIST  => $this->get_author_links( (int)$id, self::SOCIAL_LINKS_LIST ),
			Author_Controller::AUTHOR_ABOUT     => Author_Controller::get_the_author_meta( 'user_description', $id ),
		];
	}

	public function hide_host(): bool {
		return (bool) $this->get_field( Member_Stream_Meta::HIDE_HOST );
	}

	public function get_guests(): array {
		$guests = $this->get_field( Member_Stream_Meta::GUESTS );

		if ( ! $guests ) {
			return [];
		}

		return $this->prepare_guests_array_data( $guests );
	}

	/**
	 * Hydrates the guests array with the necessary data if query type is set to AUTO.
	 *
	 * @param array $guests Array of guests as they are stored in ACF (Only user IDs).
	 *
	 * @return array Array of guests with hydrated data.
	 */
	private function prepare_guests_array_data( array $guests ): array {
		$guests_array = [];

		foreach ( $guests as $guest ) {
			$guest[ Member_Stream_Meta::GUEST_LINKS ] = array_filter( $guest[ Member_Stream_Meta::GUEST_LINKS ], function ( $value ) {
				return $value;
			} );

			// If query type is set to auto, we need to populate the guest array with the guest's user meta.
			if ( $guest[ Member_Stream_Meta::GUEST_QUERY_TYPE ] === Member_Stream_Meta::GUEST_QUERY_AUTO ) {
				$guest_id = $guest[ Member_Stream_Meta::GUEST_USER ];

				if ( ! $guest_id ) {
					continue;
				}

				if ( $guest[ Member_Stream_Meta::GUEST_QUERY_AUTO_OVERRIDE ] === true ) {
					// Build the guest array using the override fields as defaults, and if they're empty, use the user's meta fields.
					$guest_array = [
						Member_Stream_Meta::GUEST_IMAGE => $guest[ Member_Stream_Meta::GUEST_IMAGE ] ?: get_avatar_url( $guest_id ),
						Member_Stream_Meta::GUEST_NAME  => $guest[ Member_Stream_Meta::GUEST_NAME ] ?: Author_Controller::get_author_display_name( $guest_id ),
						Member_Stream_Meta::GUEST_TITLE => $guest[ Member_Stream_Meta::GUEST_TITLE ] ?: Author_Controller::get_the_author_meta( 'user_title', $guest_id ),
						Member_Stream_Meta::GUEST_BIO   => $guest[ Member_Stream_Meta::GUEST_BIO ] ?: Author_Controller::get_the_author_meta( 'user_description', $guest_id ),
						Member_Stream_Meta::GUEST_LINKS => $guest[ Member_Stream_Meta::GUEST_LINKS ] ?: $this->get_author_links( (int)$guest_id, self::SOCIAL_LINKS_LIST ),
					];
				} else {
					// Build the guest array using only the user's meta fields.
					$guest_array = [
						Member_Stream_Meta::GUEST_IMAGE => get_avatar_url( $guest_id ),
						Member_Stream_Meta::GUEST_NAME  => Author_Controller::get_author_display_name( $guest_id ),
						Member_Stream_Meta::GUEST_TITLE => Author_Controller::get_the_author_meta( 'user_title', $guest_id ),
						Member_Stream_Meta::GUEST_BIO   => Author_Controller::get_the_author_meta( 'user_description', $guest_id ),
						Member_Stream_Meta::GUEST_LINKS => $this->get_author_links( (int)$guest_id, self::SOCIAL_LINKS_LIST ),
					];
				}

				$guest_array[ Member_Stream_Meta::GUEST_URL ] = get_author_posts_url( $guest_id );

				$guests_array[] = $guest_array;
			} else {
				// If set to manual, then all information is already in the guest array.
				$guests_array[] = $guest;
			}
		}

		return $guests_array;
	}

	public function get_transcript(): ?string {
		return $this->transcript;
	}

	public function get_video(): ?string {
		$video = $this->get_field( Member_Stream_Meta::VIDEO );

		if ( ! $video ) {
			return null;
		}

		$video = apply_filters( 'vimeo_clear_link', $video );

		return (string) $video;
	}

	/**
	 * Check for fallback image
	 *
	 * @return bool
	 */
	public function has_fallback_image(): bool {
		return ! empty( $this->get_field( Member_Stream_Meta::IMAGE ) );
	}

	/**
	 * Get the fallback image
	 *
	 * Get the fallback image displayed in place of video to users without access.
	 *
	 * @return array
	 */
	public function get_fallback_image(): array {
		return [
			Image_Controller::IMG_ID   	   => (int) $this->get_field( Member_Stream_Meta::IMAGE ),
			Image_Controller::SRC_SIZE 	   => Image_Sizes::SIXTEEN_NINE_GRID,
			Image_Controller::USE_LAZYLOAD => false,
			Image_Controller::SRCSET_SIZES => [
				Image_Sizes::SIXTEEN_NINE,
				Image_Sizes::SIXTEEN_NINE_LARGE,
			],
		];
	}

	/**
	 * Get the featured image
	 *
	 * @return array
	 */
	public function get_featured_image_as_fallback(): array {
		if ( ! has_post_thumbnail() ) {
			return [];
		}

		return [
			Image_Controller::IMG_ID   => (int) get_post_thumbnail_id(),
			Image_Controller::SRC_SIZE => Image_Sizes::SIXTEEN_NINE,
			Image_Controller::USE_LAZYLOAD => false,
		];
	}

	/**
	 * Returns whether the user has watched the video.
	 *
	 * @return bool True if video is in user watched list.
	 */
	public function user_has_watched(): bool {
		$video_id = get_the_ID();
		$stream   = new Member_Stream( $video_id );

		return $stream->has_user_watched_video();
	}

	/**
	 * Get repeater fields list of Outcomes
	 *
	 * @return array
	 */
	public function get_outcomes(): array {
		$outcomes = $this->get_field( Member_Stream_Meta::OUTCOMES );

		if ( ! $outcomes ) {
			return [];
		}

		return $outcomes;
	}

	/**
	 *
	 * Get field for number of seats
	 *
	 */
	public function get_seats(): string {
		if ( $this->get_field( Member_Stream_Meta::SEATS_LEFT ) ) {
			return $this->get_field( Member_Stream_Meta::SEATS_LEFT );
		}

		return '';
	}

	/**
	 *
	 * Bool to show the countdown or not
	 *
	 */
	public function show_countdown(): bool {
		if ( $this->get_field( Member_Stream_Meta::SHOW_COUNTDOWN ) ) {
			return $this->get_field( Member_Stream_Meta::SHOW_COUNTDOWN );
		}

		return false;
	}

	/**
	 *
	 * Arguments to send to Countdown component
	 *
	 */
	public function get_countdown_args(): array {
		// format END_DATE to be in the format of Y-m-d H:i:s
		$date = get_field( Member_Stream_Meta::DATE );
		$time = get_field( Member_Stream_Meta::TIME );
		$formattedDate = ( new DateTime( $date . ' ' . $time, wp_timezone() ) )->format( 'c' );

		return [
			Countdown_Controller::END_DATE  => $formattedDate,
		];
	}

	public function get_multiauthors(): array {
		$guests = $this->get_field( Member_Stream_Meta::GUESTS );

		if ( ! $guests ) {
			return [];
		}

		$authors = [];

		foreach ( $guests as $guest ) {
			if ( $guest[ Member_Stream_Meta::GUEST_QUERY_TYPE ] === Member_Stream_Meta::GUEST_QUERY_AUTO ) {
				$authors[] = $guest[ Member_Stream_Meta::GUEST_USER ];
			} else {
				$guest_website = $guest[ Member_Stream_Meta::GUEST_LINKS ][ Author_Controller::LINK_WEBSITE ];
				$guest_info = [
					Member_Stream_Meta::GUEST_NAME  => $guest[ Member_Stream_Meta::GUEST_NAME ],
					Author_Controller::LINK_WEBSITE => $guest_website,
					Member_Stream_Meta::GUEST_IMAGE => $guest[ Member_Stream_Meta::GUEST_IMAGE ],
				];
				$authors[] = $guest_info;
			}
		}

		return $authors;
	}

	/**
	 * Get repeater fields list of Resources
	 *
	 * @return array
	 */
	public function get_resources(): array {
		$resources = $this->get_field( Member_Stream_Meta::RESOURCES );

		if ( ! $resources ) {
			return [];
		}

		return $resources;
	}

	/**
	 * Get file link
	 *
	 * @param $link_args
	 *
	 * @return array
	 */
	public function get_file_link( $link_args ): array {
		if ( empty( $link_args[ Member_Stream_Meta::RESOURCE_LINK ] ) ) {
			return [];
		}

		$link = wp_parse_args( $link_args, [
			Member_Stream_Meta::RESOURCE_DISPLAY_NAME => '',
			Member_Stream_Meta::RESOURCE_FILE_TYPE 	  => '',
			Member_Stream_Meta::RESOURCE_LINK  		  => '',
			Member_Stream_Meta::RESOURCE_ACCESS_LEVEL => '',
			'target'    							  => '_blank',
		] );

		$locked    = false;
		$disabled  = false;
		$file_type = '';

		if ( ! empty( $link[ Member_Stream_Meta::RESOURCE_FILE_TYPE ] ) ) {
			$file_type = '(.' . $link[ Member_Stream_Meta::RESOURCE_FILE_TYPE ] . ')';
		}

		// Check the access level of the resource
		if ( $link[ Member_Stream_Meta::RESOURCE_ACCESS_LEVEL ] === Member_Stream_Meta::ACCESS_LEVEL_PAID && ! $this->user_has_paid_account ) {
			// Only show the modal when the user is logged in
			$link[ Member_Stream_Meta::RESOURCE_LINK ] = $this->user_has_free_account ? '#modal-id-create-paid-account' : ''; 
			$this->has_create_paid_account_modal = $this->user_has_free_account;
			$locked   = true;
			$disabled = ! $this->user_has_free_account;

			$current_url = $this->get_current_url();
		} elseif ( $link[ Member_Stream_Meta::RESOURCE_ACCESS_LEVEL ] === Member_Stream_Meta::ACCESS_LEVEL_FREE && ! $this->user_has_free_account ) {
			// Disabled for now: '#modal-id-create-free-account'
			$link[ Member_Stream_Meta::RESOURCE_LINK ] = ''; 
			$this->has_create_free_account_modal = true;
			$disabled = true;
			$locked   = true;
		}

		if ( $locked ) {
			$icon_path = '/wp-content/themes/core/assets/img/icons/icon-lock.svg';
			$icon_class = [ 'item-single__resources__file-item__img--lock' ];
		} else {
			$icon_path = '/wp-content/themes/core/assets/img/icons/icon-file.svg';
		}

		$link_classes = [
			'item-single__resources__file-item',
		];

		if ( $disabled ) {
			$link_classes[] = 'item-single__resources__file-item--disabled';
		}

		if ( $locked ) {
			$link_classes[] = 'item-single__resources__file-item--locked';
		}

		$link_content = esc_html( $link[ Member_Stream_Meta::RESOURCE_DISPLAY_NAME ] ) ?: basename( $link[ Member_Stream_Meta::RESOURCE_LINK ] );

		return [
			Link_Controller::URL     => esc_url( $link[ Member_Stream_Meta::RESOURCE_LINK ] ),
			Link_Controller::CONTENT => $link_content . ' ' . $file_type,
			Link_Controller::TARGET  => esc_attr( $link['target'] ),
			Link_Controller::ATTRS   => [ 'style' => "--icon: url({$icon_path})" ],
			Link_Controller::CLASSES => $link_classes,
		];
	}

	/**
	 * Get repeater fields list of Resources
	 *
	 * @return array
	 */
	public function get_related_courses(): array {
		$related_courses = $this->get_field( Member_Stream_Meta::RELATED_COURSES );

		if ( ! $related_courses ) {
			return [];
		}

		return $related_courses;
	}

	/**
	 * Get the course html
	 * 
	 * @param array $course
	 * 
	 * @return Deferred_Component|null
	 */
	public function get_course( array $course ): ?Deferred_Component {
		if ( empty( $course[ Member_Stream_Meta::COURSE_CTA ] ) ) {
			return null;
		}

		$cta = $course[ Member_Stream_Meta::COURSE_CTA ];

		$image = defer_template_part( 'components/image/image', null, [
			Image_Controller::IMG_ID   	   => (int) $course[ Member_Stream_Meta::COURSE_IMAGE ],
			Image_Controller::SRC_SIZE 	   => Image_Sizes::FOUR_THREE_SMALL,
			Image_Controller::LINK_URL     => $this->link[ 'url' ] ?? '',
			Image_Controller::LINK_TARGET  => $this->link[ 'target' ] ?? '',
		] );

		$title = defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'h4',
			Text_Controller::CLASSES => [ 'item-single__related_courses__title' ],
			Text_Controller::CONTENT => $course[ Member_Stream_Meta::COURSE_TITLE ],
		] );

		$member_only = defer_template_part( 'components/container/container', null, [
			Container_Controller::CONTENT => __( 'Members Only Content', 'tribe' ),
			Container_Controller::CLASSES => [ 'item-single__related_courses__members-only-warning' ],
			Container_Controller::TAG     => 'div',
		] );

		$content_container = defer_template_part( 'components/container/container', null, [
			Container_Controller::CONTENT => $member_only . $title,
			Container_Controller::CLASSES => [ 'item-single__related_courses__content' ],
			Container_Controller::TAG     => 'div',
		] );

		return defer_template_part( 'components/link/link', null, [
			Link_Controller::URL     => $cta['url'],
			Link_Controller::CONTENT => $image . $content_container,
			Link_Controller::TARGET  => $cta['target'],
			Link_Controller::CLASSES => [ 'item-single__related_courses__cta' ],
		] );
	}

	public function get_lock_icon(): string {
		return <<<SVG
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="24" viewBox="0 0 20 24">
			<path d="M17.772,10.926h.571A1.633,1.633,0,0,1,20,12.526c0,.045,0,.09,0,.135V22.3a1.644,1.644,0,0,1-.545,1.3,1.552,1.552,0,0,1-1.041.416H1.694a1.652,1.652,0,0,1-1.322-.591,1.461,1.461,0,0,1-.367-.906Q0,22.369,0,22.221v-9.5a2.417,2.417,0,0,1,.061-.612,1.614,1.614,0,0,1,1.5-1.177c.2-.009.415,0,.655,0v-.322c0-1.022-.009-2.046,0-3.068A6.96,6.96,0,0,1,3.293,3.779a7.835,7.835,0,0,1,7.756-3.7,7.743,7.743,0,0,1,6.475,5.591,7.711,7.711,0,0,1,.249,1.943v3.31ZM5.561,10.892c.319.055,8.654.045,8.862-.009,0-.049.013-.1.013-.155,0-1.085.005-2.171,0-3.256a4.2,4.2,0,0,0-.09-.748,4.459,4.459,0,0,0-8.8.817c-.014,1.058,0,2.113,0,3.175,0,.053.007.107.012.174" transform="translate(0.001 -0.012)" fill="#004cff"/>
		</svg>
		SVG;
	}

	public function get_restricted_video_message(): ?string {
		if ( $this->paid_account_required ) {
			return get_field( Member_Stream_Settings_Meta::RESTRICTED_VIDEO_PAID_MESSAGE, 'option' ) ?? __( 'This video is available exclusively to <strong>members</strong>. Please log in or become a member to watch it.', 'tribe' );
		} elseif ( $this->free_account_required ) {
			return get_field( Member_Stream_Settings_Meta::RESTRICTED_VIDEO_FREE_MESSAGE, 'option' ) ?? __( 'This video is available exclusively to <strong>registered users</strong>. Please log in or create an account to watch it.', 'tribe' );
		}

		return null;
	}

	/**
	 * Get the login html
	 * 
	 * @param bool $show_oauth_buttons
	 * 
	 * @return Deferred_Component
	 */
	private function get_login_html( bool $show_oauth_buttons = false ): Deferred_Component {
		$content = '';

		if ( $show_oauth_buttons && ( $oauth_buttons = $this->get_oauth_buttons() ) ) {
			$content .= defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'p',
				Text_Controller::CONTENT => __( 'Or continue with', 'tribe' ) . $oauth_buttons,
			] );
		}

		$content .= defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'p',
			Text_Controller::CONTENT => __( 'Have an account?', 'tribe' ) . $this->get_login_link(),
		] );

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::TAG     => 'div',
			Container_Controller::CLASSES => [ 'item-single__stream-details__aside__content-wrapper__oauth' ],
			Container_Controller::CONTENT => $content,
		] );
	}

	public function get_free_account_instructions_and_form_html() : ?Deferred_Component {
		if ( ! $this->subscribe_free_account_title || ! $this->subscribe_free_account_description ) {
			return null;
		}

		$content = defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'h3',
			Text_Controller::CLASSES => [
				'item-single__subscribe-form__title',
				'h6'
			],
			Text_Controller::CONTENT => $this->subscribe_free_account_title,
		] );
		
		$content .= defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'p',
			Text_Controller::CLASSES => [
				'item-single__subscribe-form__description',
				'p'
			],
			Text_Controller::CONTENT => $this->subscribe_free_account_description,
		] );

		if ( $this->subscribe_free_account_gf_id ) {
			$content .= gravity_form( $this->subscribe_free_account_gf_id, false, false, false, '', true, 0, false );
		}

		$content .= $this->get_login_html( true );

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CONTENT => $content,
			Container_Controller::CLASSES => [ 'item-single__subscribe-form__leadin' ],
			Container_Controller::TAG     => 'div',
		] );
	}

	/**
	 * Get the paid account cta html
	 * 
	 * @param bool $show_description
	 * @param bool $show_login_link
	 * 
	 * @return string|null
	 */
	public function get_paid_account_instructions_and_cta_html( bool $show_description = true, bool $show_login_link = true ): ?string {
		if ( ! $this->subscribe_paid_account_title ) {
			return null;
		}

		$membership_content = defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'h3',
			Text_Controller::CLASSES => [ 'item-single__membership-perks__title', 'h4' ],
			Text_Controller::CONTENT => $this->subscribe_paid_account_title,
		] );

		if ( $show_description && $this->subscribe_paid_account_description ) {
			$membership_content .= defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'p',
				Text_Controller::CLASSES => [ 'item-single__membership-perks__description' ],
				Text_Controller::CONTENT => $this->subscribe_paid_account_description,
			] );
		}

		if ( $this->subscribe_paid_account_benefits ) {
			$membership_list = array_map( function ( $list_item ) {
				return defer_template_part( 'components/text/text', null, [
					Text_Controller::TAG     => 'li',
					Text_Controller::CLASSES => [ 'item-single__membership-perks__list__item' ],
					Text_Controller::CONTENT => $list_item,
				] );
			}, $this->subscribe_paid_account_benefits );

			$membership_content .= defer_template_part( 'components/container/container', null, [
				Container_Controller::CONTENT => implode( '', $membership_list ),
				Container_Controller::CLASSES => [ 'item-single__membership-perks__list' ],
				Container_Controller::TAG     => 'ul',
			] );
		}

		if ( $this->subscribe_paid_account_cta && ! empty( $this->subscribe_paid_account_cta[ 'url' ] ) ) {
			$membership_content .= defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => $this->subscribe_paid_account_cta[ 'url' ],
				Link_Controller::CONTENT => $this->subscribe_paid_account_cta[ 'title' ] ?? __( 'Become a Member', 'tribe' ),
				Link_Controller::CLASSES => [ 'a-btn' ],
			] );
		}

		$content = defer_template_part( 'components/container/container', null, [
			Container_Controller::CONTENT => $membership_content,
			Container_Controller::CLASSES => [ 'item-single__membership-perks' ],
			Container_Controller::TAG     => 'div',
		] );

		if ( $show_login_link ) {
			$content .= $this->get_login_html();
		}

		return $content;
	}

	public function get_subscribe_upcoming_event_embed_html() : ?string {
		if ( $registration_big_marker_embed = $this->get_field( Member_Stream_Meta::BIG_MARKER_EMBED ) ) {
			return $registration_big_marker_embed;
		}

		return null;
	}

	public function get_subscribe_upcoming_event_form_html() : ?string {
		if ( $registration_form_id = $this->get_field( Member_Stream_Meta::GRAVITY_FORM_EMBED ) ) {
			return gravity_form( $registration_form_id, false, false, false, '', true, 0, false );
		}

		return null;
	}

	public function get_video_content_gate(): string {
		return (string) $this->get_field( Member_Stream_Meta::CONTENT_GATE ) ?? Member_Stream_Meta::IS_PUBLIC_VIDEO;
	}

	public function get_transcript_tab_button_classes(): string {
		$classes = [ 'item-single__content-tabs-button' ];
		
		if ( ! $this->is_public_video() && ! $this->current_user_has_access() ) {
			$classes[] = 'item-single__content-tabs-button--disabled';
		}

		return Markup_Utils::class_attribute( $classes );
	}

	public function get_transcript_tab_button_attrs(): string {
		$attrs = [ 
			'aria-controls' => 'transcript',
			'aria-selected' => 'false',
		];

		if ( $this->is_public_video() || $this->current_user_has_access() ) {
			$attrs[ 'data-js' ] = 'item-single__content-tabs-button';
		}

		return Markup_Utils::concat_attrs( $attrs );
	}

	public function get_oauth_buttons() : ?string {
		if ( ! class_exists( 'Bwz_OAuth_Login' ) ) {
			return null;
		}

		return do_shortcode( '[bwz_oauth_login_google_login]' ) . do_shortcode( '[bwz_oauth_login_linkedin_login]' );
	}

	/**
	 * Get featured link
	 *
	 * @return Deferred_Component
	 */
	public function get_login_link() : Deferred_Component {
		$args = [
			Link_Controller::CONTENT => 'Login',
			Link_Controller::URL     => wp_login_url( get_permalink() ),
			Link_Controller::TARGET  => '',
			Link_Controller::ID      => '',
			Link_Controller::CLASSES => [ 'a-cta--secondary' ]
		];

		return defer_template_part(
			'components/link/link',
			null,
			$args
		);
	}

	public function get_event_date_raw(): ?string {
		$date = $this->get_field( Member_Stream_Meta::DATE );
		$time = $this->get_field( Member_Stream_Meta::TIME );

		if ( ! $date ) {
			return null;
		}

		return $this->format_event_date( $date . ( $time ? ' ' . $time : '' ), 'c' );
	}

	public function get_free_account_banner(): ?Deferred_Component {
		$content = apply_filters( 'the_content', $this->subscribe_free_account_description );

		if ( $this->subscribe_free_account_gf_id ) {
			$content .= gravity_form( $this->subscribe_free_account_gf_id, false, false, false, '', true, 0, false );
		}

		$content .= $this->get_login_html( true );

		return defer_template_part( 'components/blocks/media_text/media_text', null, [
			Media_Text_Block_Controller::HEADING_TAG => Media_Text::HEADING_TAG_H4,
			Media_Text_Block_Controller::TITLE       => __( 'Create a Free Account to access this resource', 'tribe' ),
			Media_Text_Block_Controller::DESCRIPTION => $content,
			Media_Text_Block_Controller::LAYOUT      => Media_Text::MEDIA_LEFT,
			Media_Text_Block_Controller::IMAGE       => $this->subscribe_free_account_popup_image,
		] );
	}

	public function get_paid_account_banner( bool $is_resource_modal = false ): ?Deferred_Component {
		if ( ! $this->subscribe_paid_account_title ) {
			return null;
		}

		$cta = [];

		if ( $this->subscribe_paid_account_cta && ! empty( $this->subscribe_paid_account_cta[ 'url' ] ) ) {
			$cta = [
				'content' => $this->subscribe_paid_account_cta[ 'title' ] ?? __( 'Become a Member', 'tribe' ),
				'url'     => $this->subscribe_paid_account_cta[ 'url' ],
			];
		}

		return defer_template_part( 'components/blocks/media_text/media_text', null, [
			Media_Text_Block_Controller::HEADING_TAG    => Media_Text::HEADING_TAG_H2,
			Media_Text_Block_Controller::TITLE          => $is_resource_modal ? __( 'Become a member to access this resource', 'tribe' ) : $this->subscribe_paid_account_banner_title,
			Media_Text_Block_Controller::DESCRIPTION    => apply_filters( 'the_content', $this->subscribe_paid_account_description ),
			Media_Text_Block_Controller::FOOTER_CONTENT => $is_resource_modal ? $this->get_login_html() : null,
			Media_Text_Block_Controller::LAYOUT         => Media_Text::MEDIA_LEFT_LIST,
			Media_Text_Block_Controller::IMAGE          => $is_resource_modal ? $this->subscribe_paid_account_popup_image : $this->subscribe_paid_account_banner_image,
			Media_Text_Block_Controller::LIST           => $this->subscribe_paid_account_benefits,
			Media_Text_Block_Controller::CTA            => $cta,
		] );
	}

	public function get_sponsor_image(): ?Deferred_Component {
		if ( ! $image = $this->get_field( Member_Stream_Meta::SPONSOR_IMAGE ) ) {
			return null;
		}

		return defer_template_part( 'components/image/image', null, [
			Image_Controller::IMG_ID       => $image,
			Image_Controller::LINK_URL     => $this->get_field( Member_Stream_Meta::SPONSOR_LINK ) ?? null,
			Image_Controller::CLASSES      => [ 'item-single__stream-details__sponsor__logo' ],
			Image_Controller::IMG_ALT_TEXT => __( 'Sponsored By', 'tribe' ),
			Image_Controller::LINK_TARGET  => '_blank',
		] );
	}

	public function get_paid_account_modal() : Deferred_Component {
		return defer_template_part( 'components/modal/modal', null, [
			Modal_Controller::CLASSES => [
				'c-modal',
				'c-modal__modal-id-create-paid-account',
				'c-modal__modal-hidden',
				'item-single__stream__modal-paid-account',
			],
			Modal_Controller::CONTENT => $this->get_paid_account_banner( true ),
		] );
	}

	public function get_free_account_modal() : Deferred_Component {
		return defer_template_part( 'components/modal/modal', null, [
			Modal_Controller::CLASSES => [
				'c-modal',
				'c-modal__modal-id-create-free-account',
				'c-modal__modal-hidden',
				'item-single__stream__modal-free-account',
			],
			Modal_Controller::CONTENT => $this->get_free_account_banner(),
		] );
	}

	public function show_create_free_account_modal() : bool {
		return $this->has_create_free_account_modal;
	}

	public function show_create_paid_account_modal() : bool {
		return $this->has_create_paid_account_modal;
	}
}
