<?php
declare( strict_types=1 );

use Tribe\Project\Templates\Components\routes\single\Single_Stream_Controller;

$c = Single_Stream_Controller::factory();

$c->render_header();

?>

<main id="main-content" <?php echo $c->show_membership_menu() ? 'class="has-membership-menu"' : ''; ?>>
	<article class="item-single item-single--stream" data-js="item-single">
		<div class="l-container">
			<div class="item-single__content-wrapper item-single__stream-details">
				<div class="item-single__stream-details__header">
					<div class="item-single__category-meta">
						<?php if ( $c->get_primary_category_link_args() ) { ?>
							<div class="item-single__category-link">
								<a href="<?php echo $c->get_primary_category_link_args()->link; ?>">
									<?php echo $c->get_primary_category_link_args()->label; ?>
								</a>
							</div>
						<?php } ?>

						<?php if ( $c->get_event_type() ) { ?>
							<span class="item-single__event-type">
								<?php echo $c->get_event_type(); ?>
							</span>
						<?php } ?>
					</div>

					<?php get_template_part( 'components/text/text', null, $c->get_title_args() ); ?>

					<?php if ( $c->is_upcoming_event() && ( $date = $c->get_event_date() ) && ( $raw_date = $c->get_event_date_raw() ) ) { ?>
						<div class="item-single__date event-date-container" data-date="<?php echo $raw_date; ?>">
							<?php if ( $date ) { ?>
								<span class="item-single__date-day">
									<i class="icon icon-calendar"></i>
									<span class="event-date-day"><?php echo esc_html( $date ); ?></span>
								</span>
							<?php } ?>
							<?php if ( $time = $c->get_event_time() ) { ?>
								<span class="item-single__date-time">
									<i class="icon icon-clock"></i>
									<span class="event-date-time"><?php echo esc_html( $time ); ?></span>
								</span>
							<?php } ?>
						</div>
					<?php } ?>

					<?php get_template_part( 'components/post_info/post_info', null, [
						'show_author' 		=> true,
						'show_avatar' 		=> true,
						'show_date'   		=> false,
						'prefix'      		=> 'with',
						'author_args' 		=> $c->get_author_args( [
								'size'             => 55,
								'show_description' => false,
						] ),
						'multiple_authors'	=> $c->get_multiauthors(),
					] ); ?>
				</div>

				<div class="item-single__stream-details__sponsor">
					<?php if ( $sponsor_logo = $c->get_sponsor_image() ) : ?>
						<span class="t-overline"><?php _e( 'Sponsored By', 'tribe' ); ?></span>
						<?php echo $sponsor_logo; ?>
					<?php endif; ?>

					<?php if ( $c->show_countdown() && $c->is_upcoming_event() && $c->current_user_has_access() ) : ?>
						<?php get_template_part( 'components/countdown/countdown', null, $c->get_countdown_args() ); ?>
					<?php endif; ?>
				</div>

				<div class="item-single__stream-details__content">
					<?php if ( ! $c->is_public_video() && ! $c->current_user_has_access() ) : ?>
						<div class="item-single__video-placeholder t-sink">
							<?php 
							
							if ( $c->has_fallback_image() ) {
								get_template_part( 'components/image/image', null, $c->get_fallback_image() );
							}
							
							?>
							<div class="item-single__video-placeholder-overlay">
								<div class="item-single__restricted-content">
									<div class="item-single__restricted-content__message-container">
										<?php echo $c->get_lock_icon(); ?>
										<h6 class="item-single__restricted-content__message"><?php _e( 'Access restricted', 'tribe' ); ?></h6>
									</div>
									<div class="item-single__restricted-content__description"><?php echo $c->get_restricted_video_message(); ?></div>
								</div>
							</div>
						</div>
					<?php else : ?>
						<?php if ( $c->get_video() ) : ?>
							<div class="item-single__video-container">
								<div class="item-single__embed-container item-single__embed-container--video">
									<?php echo $c->get_video(); ?>
								</div>
							</div>
						<?php elseif ( $c->has_fallback_image() ) : ?>
							<div class="item-single__video-placeholder t-sink">
								<?php get_template_part( 'components/image/image', null, $c->get_fallback_image() ); ?>
								<div class="item-single__video-placeholder-overlay">
									<div class="item-single__restricted-content">
										<div class="item-single__restricted-content__description"><p><?php _e( 'Video is coming soon.', 'tribe' ); ?></p></div>
									</div>
								</div>
							</div>
						<?php endif; ?>
					<?php endif; ?>

					<?php if ( wp_is_mobile() ) : ?>
						<div class="item-single__share">
							<?php if ( $c->is_on_demand() && $c->current_user_has_access() ) : ?>
								<button
									class="a-btn watched-button <?php echo $c->user_has_watched() ? 'watched' : ''; ?>"
									data-js="video-progress"
									data-nonce="<?php echo wp_create_nonce( 'wp_rest' ); ?>"
									data-user="<?php echo get_current_user_id(); ?>"
									data-post="<?php echo get_the_ID(); ?>"
								>
									<span class="watched-button__copy"><?php $c->user_has_watched() ? _e( 'Watched', 'tribe' ) : _e( 'Mark as Watched', 'tribe' ); ?></span> <span class="watched-button__icon"></span>
								</button>
							<?php endif; ?>
							<?php get_template_part( 'components/share/share' ) ?>
						</div>
					<?php endif; ?>

					<?php if ( wp_is_mobile() ) : ?>
						<?php if ( $c->is_free_account_required() && ! $c->current_user_has_access() && ( $html = $c->get_free_account_instructions_and_form_html() ) ) : ?>
							<div class="item-single__stream-details__aside__content-wrapper">
								<?php echo $html; ?>
							</div>
						<?php endif; ?>

						<?php if ( $c->is_paid_account_required() && ! $c->current_user_has_access() && ( $html = $c->get_paid_account_instructions_and_cta_html( false ) ) ) : ?>
							<div class="item-single__stream-details__aside__content-wrapper">
								<?php echo $html; ?>
							</div>
						<?php endif; ?>

						<?php if ( $c->is_upcoming_event() && ( $c->is_public_video() || $c->current_user_has_access() ) ) : ?>
							<?php if ( $html = $c->get_subscribe_upcoming_event_embed_html() ) : ?>
								<?php echo $html; ?>
							<?php elseif ( $html = $c->get_subscribe_upcoming_event_form_html() ) : ?>
								<div class="item-single__stream-details__aside__content-wrapper">
									<?php echo $html; ?>
								</div>
							<?php endif; ?>
						<?php endif; ?>
					<?php endif; ?>

					<div class="item-single__tabs-wrapper">
						<div class="item-single__tabs-container">
							<button class="item-single__content-tabs-button" data-js="item-single__content-tabs-button" aria-controls="overview" aria-selected="true">
								<?php _e( 'Overview', 'tribe' ); ?>
							</button>

							<?php if ( $c->get_transcript() ) : ?>
								<button <?php echo $c->get_transcript_tab_button_classes() ?> <?php echo $c->get_transcript_tab_button_attrs() ?>>
									<?php _e( 'Transcript', 'tribe' ); ?>
								</button>
							<?php endif; ?>
						</div>

						<?php if ( ! wp_is_mobile() ) : ?>
							<div class="item-single__share">
								<?php if ( $c->is_on_demand() && $c->current_user_has_access() ) : ?>
									<button
										class="a-btn watched-button <?php echo $c->user_has_watched() ? 'watched' : ''; ?>"
										data-js="video-progress"
										data-nonce="<?php echo wp_create_nonce( 'wp_rest' ); ?>"
										data-user="<?php echo get_current_user_id(); ?>"
										data-post="<?php echo get_the_ID(); ?>"
									>
										<span class="watched-button__copy"><?php $c->user_has_watched() ? _e( 'Watched', 'tribe' ) : _e( 'Mark as Watched', 'tribe' ); ?></span> <span class="watched-button__icon"></span>
									</button>
								<?php endif; ?>
								<?php get_template_part( 'components/share/share' ) ?>
							</div>
						<?php endif; ?>
					</div>

					<div class="item-single__tab-content s-sink t-sink" data-js="item-single__tab-content" aria-labelledby="overview" hidden="false">
						<?php if ( ! empty( $c->get_outcomes() ) ) : ?>
							<?php foreach ( $c->get_outcomes() as $key => $outcome ) : ?>
								<?php get_template_part( 'components/outcomes/outcomes', null, $outcome ); ?>
							<?php endforeach; ?>
						<?php endif; ?>

						<?php if ( $description_first_part = $c->get_description_first_part() ) : ?>
							<?php echo $description_first_part; ?>
						<?php endif; ?>

						<?php if ( $description_second_part = $c->get_description_second_part() ) : ?>
							<?php echo $description_second_part; ?>
						<?php endif; ?>
					</div>

					<?php if ( $c->get_transcript() && ( $c->is_public_video() || $c->current_user_has_access() ) ) { ?>
						<div class="item-single__tab-content" data-js="item-single__tab-content" aria-labelledby="transcript" hidden="true">
							<div class="item-single__transcript-container">
								<?php echo $c->get_transcript(); ?>
							</div>
						</div>
					<?php } ?>
				</div>

				<div class="item-single__stream-details__aside">
					<?php if ( ! wp_is_mobile() ) : ?>
						<?php if ( $c->is_free_account_required() && ! $c->current_user_has_access() && ( $html = $c->get_free_account_instructions_and_form_html() ) ) : ?>
							<div class="item-single__stream-details__aside__content-wrapper">
								<?php echo $html; ?>
							</div>
						<?php endif; ?>

						<?php if ( $c->is_paid_account_required() && ! $c->current_user_has_access() && ( $html = $c->get_paid_account_instructions_and_cta_html( false ) ) ) : ?>
							<div class="item-single__stream-details__aside__content-wrapper">
								<?php echo $html; ?>
							</div>
						<?php endif; ?>

						<?php if ( $c->is_upcoming_event() && ( $c->is_public_video() || $c->current_user_has_access() ) ) : ?>
							<?php if ( $html = $c->get_subscribe_upcoming_event_embed_html() ) : ?>
								<?php echo $html; ?>
							<?php elseif ( $html = $c->get_subscribe_upcoming_event_form_html() ) : ?>
								<div class="item-single__stream-details__aside__content-wrapper">
									<?php echo $html; ?>
								</div>
							<?php endif; ?>
						<?php endif; ?>
					<?php endif; ?>
					
					<?php
					
					$resources       = $c->get_resources();
					$related_courses = $c->get_related_courses();

					$show_related_courses = $related_courses && $c->is_on_demand() && ( $c->is_public_video() || $c->current_user_has_access() );

					if ( $resources || $show_related_courses ) :
						?>
						<div class="item-single__stream-details__aside__content-wrapper">
							<div class="item-single__related-content-container">
								<?php if ( $resources ) : ?>
									<div class="item-single__resources">
										<h3 class="item-single__sub_title h5"><?php _e( 'Resources', 'tribe' ); ?></h3>
										<?php foreach ( $resources as $resource ) : ?>
											<?php get_template_part( 'components/link/link', null, $c->get_file_link( $resource ) ); ?>
										<?php endforeach; ?>
									</div>
								<?php endif; ?>
								<?php if ( $show_related_courses ) : ?>
									<div class="item-single__related_courses">
										<h3 class="item-single__sub_title h5"><?php _e( 'Related Courses', 'tribe' ); ?></h3>
										<?php foreach ( $related_courses as $course ) : ?>
											<?php echo $c->get_course( $course ); ?>
										<?php endforeach; ?>
									</div>
								<?php endif; ?>
							</div>
						</div>
						<?php
					endif;
					?>
				</div>
			</div>
			<?php 
				
			$stream_guests             = $c->get_guests();
			$stream_host               = $c->get_host_args();
			$stream_participants_count = count( $stream_guests ) + ( ( $stream_guests && ! $c->hide_host() ) ? 1 : 0 );
			
			?>

			<?php if ( $stream_host || $stream_guests ) : ?>
				<div class="item-single__stream-speakers item-single__stream-speakers--<?php echo esc_attr( 1 === $stream_participants_count || 0 === $stream_participants_count % 2 ? 'half-columns' : 'third-columns' ); ?>">
					<?php if ( $stream_host && ! $c->hide_host() ) : ?>
						<?php get_template_part( 'components/author/member_stream_host', null, $c->get_host_args() ); ?>
					<?php endif; ?>

					<?php if ( ! empty( $stream_guests ) ) : ?>
						<?php foreach ( $stream_guests as $key => $guest ) : ?>
							<?php get_template_part( 'components/author/member_stream_guest', null, $guest ); ?>
						<?php endforeach; ?>
					<?php endif; ?>
				</div>
			<?php endif; ?>
		</div>
	</article>
	<?php if ( $c->is_paid_account_required() && ! $c->current_user_has_access() && ( $banner = $c->get_paid_account_banner() ) ) : ?>
		<section class="item-single__stream-footer">
			<div class="l-container">
				<?php echo $banner; ?>
			</div>
		</section>
	<?php endif; ?>
	<?php
	
	/* Disabled for now
	if ( $c->show_create_free_account_modal() ) {
		echo $c->get_free_account_modal();
	}
	*/
	
	if ( $c->show_create_paid_account_modal() ) {
		echo $c->get_paid_account_modal();
	}
	?>
</main>

<?php

$c->render_sidebar();
$c->render_footer();
