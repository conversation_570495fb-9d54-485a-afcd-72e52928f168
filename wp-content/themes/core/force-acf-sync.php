<?php
/**
 * Temporary script to force ACF field sync for SSL blocks
 * Run this once after updating the field types from textarea to WYSIWYG
 */

// Only run this in admin or via CLI
if (!is_admin() && !defined('WP_CLI')) {
    return;
}

add_action('init', function() {
    // Remove the check so it runs every time until we confirm it works
    // if (get_option('ssl_blocks_acf_synced', false)) {
    //     return;
    // }

    // List of SSL block field groups that need to be re-synced
    $ssl_blocks = [
        'sslprovideralternatives',
        'sslproviderfaqs',
        'sslproviderrelatedlinks',
        'sslpubreviewfaqs',
        'sslprovidermarketfit',
        'sslxvsyalternatives',
        'sslxvsydifferences',
        'sslxvsyfeaturecomparison',
        'sslxvsymorecomparisons',
        'sslxvsyoverview',
        'sslxvsypricingcomparison',
        'sslxvsyproscons',
        'sslxvsysimilarities',
        'sslxvsyusecases',
        'sslxvsyvideocomparison'
    ];

    // Clear ACF cache more aggressively
    if (function_exists('acf_get_field_groups')) {

        // Clear all ACF related caches
        wp_cache_flush_group('acf');

        // Delete specific ACF transients
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_acf_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_acf_%'");

        // Get all field groups and clear their cache
        $field_groups = acf_get_field_groups();

        foreach ($field_groups as $field_group) {
            // Check if this is one of our SSL blocks
            foreach ($ssl_blocks as $block_name) {
                if (strpos($field_group['key'], $block_name) !== false) {
                    // Delete the cached field group
                    wp_cache_delete('acf_get_field_group_' . $field_group['key'], 'acf');
                    wp_cache_delete('acf_get_fields_' . $field_group['key'], 'acf');

                    // Also try to delete from database cache
                    delete_transient('acf_get_field_group_' . $field_group['key']);
                    delete_transient('acf_get_fields_' . $field_group['key']);

                    error_log("Cleared ACF cache for: " . $field_group['key']);
                }
            }
        }

        // Force ACF to reload field groups
        if (function_exists('acf_reset_local')) {
            acf_reset_local();
        }

        error_log("SSL Blocks ACF sync completed!");

        // Show admin notice
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p><strong>SSL Blocks ACF Sync:</strong> Field groups cache cleared. Please refresh the page and test the WYSIWYG fields.</p>';
            echo '<p><a href="' . admin_url('edit.php?post_type=post&clear_acf_cache=1') . '" class="button">Clear ACF Cache Again</a></p>';
            echo '</div>';
        });
    }
}, 999);

// Also try to clear cache on admin_init
add_action('admin_init', function() {
    if (isset($_GET['clear_acf_cache'])) {
        wp_cache_flush_group('acf');
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_acf_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_acf_%'");

        wp_redirect(admin_url('edit.php?post_type=post&acf_cleared=1'));
        exit;
    }

    if (isset($_GET['acf_cleared'])) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p><strong>ACF Cache Cleared!</strong> Now test the SSL blocks.</p>';
            echo '</div>';
        });
    }
});
