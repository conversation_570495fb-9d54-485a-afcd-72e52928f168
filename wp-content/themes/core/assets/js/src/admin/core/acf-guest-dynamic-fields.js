// eslint-disable-next-line no-undef
jQuery( document ).ready( function( $ ) {
	if ( typeof acf === 'undefined' ) {
		return;
	}

	$( document ).on( 'change', '[data-key="field_tribe_stream_meta_guest_user"] .acf-input select', function( event ) {
		update_guest_override_fields( event, $( this ).parents( '.acf-row' ), $ );
	} );

	$( '[data-key="field_tribe_stream_meta_guest_user"] .acf-input select' ).trigger( 'ready' );
} );

function update_guest_override_fields( event, $field, $ ) {
	if ( this.request ) {
		// if a recent request has been made abort it
		this.request.abort();
	}

	const image_field = $field.find( '[data-key="field_tribe_stream_meta_guest_image"]' );
	image_field.find( 'a[data-name="remove"]' ).trigger( 'click' );

	const name_field = $field.find( '[data-key="field_tribe_stream_meta_guest_name"] input' );
	name_field.val( '' );

	const title_field = $field.find( '[data-key="field_tribe_stream_meta_guest_title"] input' );
	title_field.val( '' );

	const bio_field = $field.find( '[data-key="field_tribe_stream_meta_guest_bio"] textarea' );
	bio_field.val( '' );

	const website_field = $field.find( '[data-key="tribe_stream_meta_link"] input' );
	website_field.val( '' );

	const linkedin_field = $field.find( '[data-key="tribe_stream_meta_linkedin"] input' );
	linkedin_field.val( '' );

	const twitter_field = $field.find( '[data-key="tribe_stream_meta_twitter"] input' );
	twitter_field.val( '' );

	// get the target of the event and then get the value of that field
	const target = $( event.target );
	const guest = target.val();

	if ( ! guest ) {
		// no guest selected
		// don't need to do anything else
		return;
	}

	// set and prepare data for ajax
	let data = {
		action: 'tribe_stream_get_guest_data',
		guest: guest,
	};

	// call the acf function that will fill in other values
	// like post_id and the acf nonce
	// eslint-disable-next-line no-undef
	data = acf.prepareForAjax( data );

	// make ajax request
	// instead of going through the acf.ajax object to make requests like in <5.7
	// we need to do a lot of the work ourselves, but other than the method that's called
	// this has not changed much
	this.request = $.ajax( {
		// eslint-disable-next-line no-undef
		url: acf.get( 'ajaxurl' ), // acf stored value
		data: data,
		type: 'post',
		dataType: 'json',
		success: function( json ) {
			if ( ! json ) {
				return;
			}

			// Update selected guest image
			image_field.find( 'input[type=hidden]' ).val( json.image.url );
			image_field.find( 'img' ).attr( 'src', json.image.url );
			image_field.find( '.acf-image-uploader' ).addClass( 'has-value' );

			// add the returned data to the fields
			name_field.val( json.name );
			title_field.val( json.title );
			bio_field.val( json.bio );
			website_field.val( json.social.website );
			linkedin_field.val( json.social.linkedin );
			twitter_field.val( json.social.twitter );
		},
	} );
}
