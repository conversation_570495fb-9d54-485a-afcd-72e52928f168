<?php
declare( strict_types=1 );

namespace Tribe\Project\Object_Meta;

use Tribe\Libs\ACF\ACF_Meta_Group;
use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Field_Group;
use Tribe\Libs\ACF\Group;
use Tribe\Project\Integrations\Memberpress\Memberpress;
use Tribe\Project\Post_Types\Member_Stream\Member_Stream;
use Tribe\Libs\ACF\Repeater;
use Tribe\Project\Taxonomies\Post_State\Post_State;
use Tribe\Project\Templates\Components\Traits\Gravity_Forms_Choices;
use Tribe\Project\Templates\Components\author\Author_Controller;

class Member_Stream_Meta extends ACF_Meta_Group {
	use Gravity_Forms_Choices;

	public const NAME                      = 'tribe_stream_meta';
	public const DATE                      = 'date';
	public const TIME                      = 'time';
	public const SHOW_COUNTDOWN            = 'show_countdown';
	public const SEATS_LEFT                = 'seats_left';
	public const IMAGE                     = 'image';
	public const DESCRIPTION               = 'description';
	public const BIG_MARKER_EMBED          = 'big_marker_embed';
	public const GRAVITY_FORM_EMBED        = 'gravity_form_embed';
	public const HOST                      = 'host';
	public const HIDE_HOST                 = 'hide_host';
	public const GUESTS                    = 'guests';
	public const GUEST_QUERY_TYPE          = 'guest_query_type';
	public const GUEST_IMAGE               = 'guest_image';
	public const GUEST_NAME                = 'guest_name';
	public const GUEST_TITLE               = 'guest_title';
	public const GUEST_BIO                 = 'guest_bio';
	public const GUEST_LINKS               = 'guest_links';
	public const GUEST_URL                 = 'guest_url';
	public const RESOURCES                 = 'resources';
	public const RESOURCE_ACCESS_LEVEL     = 'resource_access_level';
	public const ACCESS_LEVEL_PAID         = 'paid';
	public const ACCESS_LEVEL_FREE         = 'free';
	public const ACCESS_LEVEL_FULL         = 'full';
	public const RESOURCE_LINK             = 'file_url';
	public const RESOURCE_DISPLAY_NAME     = 'file_name';
	public const RESOURCE_FILE_TYPE        = 'file_type';
	public const RELATED_COURSES           = 'related_courses';
	public const COURSE_TITLE              = 'course_title';
	public const COURSE_CONTENT            = 'course_content';
	public const COURSE_CTA                = 'course_cta';
	public const COURSE_IMAGE              = 'course_image';
	public const STATE                     = 'state';
	public const TRANSCRIPT                = 'transcript';
	public const VIDEO                     = 'video';
	public const DURATION                  = 'duration';
	public const DURATION_HOURS            = 'hours';
	public const DURATION_MINS             = 'minutes';
	public const UPCOMING_CTA              = 'upcoming_cta';
	public const LOGGED_OUT_LINK           = 'logged_out_link';
	public const LOGGED_OUT_MSG            = 'logged_out_msg';
	public const CUSTOM_SIDEBAR_MSG        = 'custom_sidebar_msg';
	public const OUTCOMES                  = 'outcomes';
	public const OUTCOMES_NAME             = 'outcomes_name';
	public const OUTCOMES_LIST             = 'outcomes_list';
	public const OUTCOMES_ITEM             = 'outcomes_item';
	public const GUEST_QUERY_AUTO          = 'auto';
	public const GUEST_QUERY_MANUAL        = 'manual';
	public const GUEST_USER                = 'guest_user';
	public const GUEST_QUERY_AUTO_OVERRIDE = 'override_auto_fields';
	public const SHOW_MEMBERSHIP_NAV       = 'show_membership_nav';
	public const CONTENT_GATE   		   = 'content_gate';
	public const IS_PUBLIC_VIDEO           = 'public';
	public const IS_FREE_ACCOUNT           = 'free_acccount';
	public const IS_PAID_ACCOUNT           = 'paid_account';
	public const SPONSOR_IMAGE             = 'sponsor_image';
	public const SPONSOR_LINK              = 'sponsor_link';

	protected $post_types = [ Member_Stream::NAME ];

	protected function get_group_config(): array {
		$group = new Group( self::NAME, $this->object_types );
		$group->set_attributes( [
			'title'    => __( 'Member Stream Options', 'tribe' ),
			'location' => [
				[
					[
						'param'    => 'post_type',
						'operator' => '==',
						'value'    => Member_Stream::NAME,
					],
				],
			],
		] );

		if ( Memberpress::is_active() ) {
			$group->add_field( $this->get_show_membership_nav_field() );
		}

		// Set if the video is public or requires a paid/free account
		$group->add_field( $this->get_content_gate_field() );

		// Set event type to either on demand or upcoming
		$group->add_field( $this->get_state_field() );

		// Fields specific to on demand events
		$group->add_field( $this->get_video_field() );
		$group->add_field( $this->get_transcript_field() );
		$group->add_field( $this->get_duration_field() );
		$group->add_field( $this->get_image_field() );

		$group->add_field( $this->get_sponsor_image_field() );
		$group->add_field( $this->get_sponsor_link_field() );

		// Upcoming cta, logged out message and link are used for both event states
		//$group->add_field( $this->get_upcoming_cta_field() );
		//$group->add_field( $this->get_custom_sidebar_msg_field() );
		//$group->add_field( $this->get_logged_out_msg_field() );
		//$group->add_field( $this->get_logged_out_link_field() );

		// Fields specific to upcoming events
		$group->add_field( $this->get_show_countdown_field() );
		$group->add_field( $this->get_date_field() );
		$group->add_field( $this->get_time_field() );
		$group->add_field( $this->get_seat_count() );

		// Outcomes Repeater
		$group->add_field( $this->get_outcomes_repeater() );

		// Event description used for both event states
		$group->add_field( $this->get_description_field() );

		// Big Marker embed used for upcoming events
		$group->add_field( $this->get_big_marker_embed_field() );

		// Gravity Form embed used for upcoming events
		$group->add_field( $this->get_gravity_form_field() );

		// Host and guest repeater are used for both event states
		$group->add_field( $this->get_host_field() );
		$group->add_field( $this->get_hide_host_field() );
		$group->add_field( $this->get_guests_repeater() );

		$group->add_field( $this->resources_repeater() );
		$group->add_field( $this->related_courses_repeater() );

		return $group->get_attributes();
	}

	public function get_keys(): array {
		return [
			self::DATE,
			self::TIME,
			self::SHOW_COUNTDOWN,
			self::SEATS_LEFT,
			self::IMAGE,
			self::DESCRIPTION,
			self::BIG_MARKER_EMBED,
			self::GRAVITY_FORM_EMBED,
			self::HOST,
			self::HIDE_HOST,
			self::GUESTS,
			self::STATE,
			self::TRANSCRIPT,
			self::VIDEO,
			self::DURATION,
			self::UPCOMING_CTA,
			self::LOGGED_OUT_MSG,
			self::LOGGED_OUT_LINK,
			self::CUSTOM_SIDEBAR_MSG,
			self::OUTCOMES,
			self::OUTCOMES_NAME,
			self::OUTCOMES_LIST,
			self::OUTCOMES_ITEM,
			self::SHOW_MEMBERSHIP_NAV,
			self::CONTENT_GATE,
		];
	}

	protected function get_outcomes_repeater(): Repeater {
		$outcomes_repeater = new Repeater( self::NAME . '_' . self::OUTCOMES, [
			'label'        => __( 'What you\'ll learn', 'tribe' ),
			'name'         => self::OUTCOMES,
			'button_label' => __( 'Add List', 'tribe' ),
			'layout'       => 'block',
		] );

		$outcomes_repeater->add_field( new Field( self::NAME . '_' . self::OUTCOMES_NAME, [
			'label'         => __( 'List Name', 'tribe' ),
			'name'          => self::OUTCOMES_NAME,
			'default_value' => __( 'What you\'ll learn', 'tribe' ),
			'type'          => 'text',
		] ) );

		$outcomes_listing = new Repeater( self::NAME . '_' . self::OUTCOMES_LIST, [
			'label'        => __( 'List Items', 'tribe' ),
			'name'         => self::OUTCOMES_LIST,
			'button_label' => __( 'Add List Item', 'tribe' ),
			'layout'       => 'block',
		] );

		$outcomes_listing->add_field( new Field( self::NAME . '_' . self::OUTCOMES_ITEM, [
			'label' => __( 'List Item Text', 'tribe' ),
			'name'  => self::OUTCOMES_ITEM,
			'type'  => 'text',
		] ) );

		$outcomes_repeater->add_field( $outcomes_listing );

		return $outcomes_repeater;
	}

	protected function resources_repeater(): Repeater {
		$resources_repeater = new Repeater( self::NAME . '_' . self::RESOURCES, [
			'label'        => __( 'Resources', 'tribe' ),
			'name'         => self::RESOURCES,
			'button_label' => __( 'Add Resource', 'tribe' ),
			'layout'       => 'row',
			'min'          => 0,
			'max'          => 5,
		] );
		$resources_repeater->add_field( new Field( self::NAME . '_' . self::RESOURCE_ACCESS_LEVEL, [
			'label'         => __( 'Resource Access Level', 'tribe' ),
			'name'          => self::RESOURCE_ACCESS_LEVEL,
			'type'          => 'radio',
			'default_value' => self::ACCESS_LEVEL_PAID,
			'choices'       => [
				self::ACCESS_LEVEL_PAID => __( 'Requires Paid account', 'tribe' ),
				self::ACCESS_LEVEL_FREE => __( 'Requires Free account', 'tribe' ),
				self::ACCESS_LEVEL_FULL => __( 'Full access', 'tribe' ),
			],
			'instructions'  => __( 'This is the level of access of users to download the resource. <br>Resource with <i>Full access</i> can be downloaded by logged-in and logged-out users.', 'tribe' ),
		] ) );
		$resources_repeater->add_field( new Field( self::NAME . '_' . self::RESOURCE_DISPLAY_NAME, [
			'label' => __( 'Display Name', 'tribe' ),
			'name'  => self::RESOURCE_DISPLAY_NAME,
			'type'  => 'text',
		] ) );
		$resources_repeater->add_field( new Field( self::NAME . '_' . self::RESOURCE_LINK, [
			'label' => __( 'File URL', 'tribe' ),
			'name'  => self::RESOURCE_LINK,
			'type'  => 'url',
		] ) );
		$resources_repeater->add_field( new Field( self::NAME . '_' . self::RESOURCE_FILE_TYPE, [
			'label'        => __( 'File Type', 'tribe' ),
			'name'         => self::RESOURCE_FILE_TYPE,
			'type'         => 'text',
			'instructions' => __( 'The file extension without the "." (i.e. docx, pdf, xls, etc.', 'tribe' ),
		] ) );

		return $resources_repeater;
	}

	protected function related_courses_repeater(): Repeater {
		$related_courses_repeater = new Repeater( self::NAME . '_' . self::RELATED_COURSES, [
			'label'        => __( 'Related Courses', 'tribe' ),
			'name'         => self::RELATED_COURSES,
			'button_label' => __( 'Add Course', 'tribe' ),
			'layout'       => 'row',
			'min'          => 0,
			'max'          => 5,
		] );
		$related_courses_repeater->add_field( new Field( self::NAME . '_' . self::COURSE_TITLE, [
			'label' => __( 'Title', 'tribe' ),
			'name'  => self::COURSE_TITLE,
			'type'  => 'text',
		] ) );
		$related_courses_repeater->add_field( new Field( self::NAME . '_' . self::COURSE_CTA, [
			'label' => __( 'Course Link', 'tribe' ),
			'name'  => self::COURSE_CTA,
			'type'  => 'link',
		] ) );
		$related_courses_repeater->add_field( new Field( self::NAME . '_' . self::COURSE_IMAGE, [
			'label'         => __( 'Image', 'tribe' ),
			'name'          => self::COURSE_IMAGE,
			'type'          => 'image',
			'return_format' => 'id',
			'instructions'  => __( 'Recommended image size: 120px wide for displaying in the sidebar.', 'tribe' ),
		] ) );

		return $related_courses_repeater;
	}

	protected function get_guests_repeater(): Repeater {
		$guests_repeater = new Repeater( self::NAME . '_' . self::GUESTS, [
			'label'        => __( 'Guests', 'tribe' ),
			'name'         => self::GUESTS,
			'button_label' => __( 'Add Guest', 'tribe' ),
			'layout'       => 'block',
		] );
		$guests_repeater->add_field( new Field( self::NAME . '_' . self::GUEST_QUERY_TYPE, [
			'label'         => __( 'Query Type', 'tribe' ),
			'name'          => self::GUEST_QUERY_TYPE,
			'type'          => 'radio',
			'default_value' => self::GUEST_QUERY_MANUAL,
			'choices'       => [
				self::GUEST_QUERY_AUTO   => __( 'Auto', 'tribe' ),
				self::GUEST_QUERY_MANUAL => __( 'Manual', 'tribe' ),
			],
		] ) );
		$guests_repeater->add_field( new Field( self::NAME . '_' . self::GUEST_USER, [
			'label'             => __( 'Guest User', 'tribe' ),
			'instructions'      => __( 'Select a user to be displayed as a Guest', 'tribe' ),
			'name'              => self::GUEST_USER,
			'type'              => 'user',
			'return_format'     => 'id',
			'allow_null'        => false,
			'conditional_logic' => [
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::GUEST_QUERY_TYPE,
						'operator' => '==',
						'value'    => self::GUEST_QUERY_AUTO,
					],
				],
			],
		] ) );

		$guests_repeater->add_field( new Field( self::NAME . '_' . self::GUEST_QUERY_AUTO_OVERRIDE, [
			'label'             => __( 'Override Fields', 'tribe' ),
			'instructions'      => __( 'Allows you to change the selected guest\'s details', 'tribe' ),
			'name'              => self::GUEST_QUERY_AUTO_OVERRIDE,
			'type'              => 'true_false',
			'default_value'     => false,
			'ui'                => true,
			'conditional_logic' => [
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::GUEST_USER,
						'operator' => '!=empty',
					],
				],
			],
		] ) );

		$guests_repeater->add_field( new Field( self::NAME . '_' . self::GUEST_IMAGE, [
			'label'             => __( 'Image', 'tribe' ),
			'name'              => self::GUEST_IMAGE,
			'type'              => 'image',
			'return_format'     => 'url',
			'library'           => 'all',
			'conditional_logic' => [
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::GUEST_QUERY_TYPE,
						'operator' => '==',
						'value'    => self::GUEST_QUERY_MANUAL,
					],
				],
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::GUEST_QUERY_AUTO_OVERRIDE,
						'operator' => '==',
						'value'    => '1',
					],
				],
			],
		] ) );
		$guests_repeater->add_field( new Field( self::NAME . '_' . self::GUEST_NAME, [
			'label'             => __( 'Name', 'tribe' ),
			'name'              => self::GUEST_NAME,
			'type'              => 'text',
			'conditional_logic' => [
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::GUEST_QUERY_TYPE,
						'operator' => '==',
						'value'    => self::GUEST_QUERY_MANUAL,
					],
				],
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::GUEST_QUERY_AUTO_OVERRIDE,
						'operator' => '==',
						'value'    => '1',
					],
				],
			],
		] ) );
		$guests_repeater->add_field( new Field( self::NAME . '_' . self::GUEST_TITLE, [
			'label'             => __( 'Title', 'tribe' ),
			'name'              => self::GUEST_TITLE,
			'type'              => 'text',
			'conditional_logic' => [
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::GUEST_QUERY_TYPE,
						'operator' => '==',
						'value'    => self::GUEST_QUERY_MANUAL,
					],
				],
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::GUEST_QUERY_AUTO_OVERRIDE,
						'operator' => '==',
						'value'    => '1',
					],
				],
			],
		] ) );
		$guests_repeater->add_field( new Field( self::NAME . '_' . self::GUEST_URL, [
			'label' => __( 'Link', 'tribe' ),
			'name'  => self::GUEST_URL,
			'type'  => 'url',
			'conditional_logic' => [
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::GUEST_QUERY_TYPE,
						'operator' => '==',
						'value'    => self::GUEST_QUERY_MANUAL,
					],
				]
			]
		] ) );
		$guests_repeater->add_field( new Field( self::NAME . '_' . self::GUEST_BIO, [
			'label'             => __( 'Bio', 'tribe' ),
			'name'              => self::GUEST_BIO,
			'type'              => 'textarea',
			'conditional_logic' => [
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::GUEST_QUERY_TYPE,
						'operator' => '==',
						'value'    => self::GUEST_QUERY_MANUAL,
					],
				],
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::GUEST_QUERY_AUTO_OVERRIDE,
						'operator' => '==',
						'value'    => '1',
					],
				],
			],
		] ) );
		$guests_repeater->add_field( new Field( self::NAME . '_' . self::GUEST_LINKS, [
			'label'             => __( 'Social Links', 'tribe' ),
			'name'              => self::GUEST_LINKS,
			'type'              => 'group',
			'layout'            => 'table',
			'sub_fields'        => [
				[
					'label' => __( 'WebSite', 'tribe' ),
					'name'  => Author_Controller::LINK_WEBSITE,
					'key'   => self::NAME . '_' . Author_Controller::LINK_WEBSITE,
					'type'  => 'text',
				],
				[
					'label' => __( 'LinkedIn', 'tribe' ),
					'name'  => Author_Controller::LINK_LINKEDIN,
					'key'   => self::NAME . '_' . Author_Controller::LINK_LINKEDIN,
					'type'  => 'text',
				],
				[
					'label' => __( 'Twitter', 'tribe' ),
					'name'  => Author_Controller::LINK_TWITTER,
					'key'   => self::NAME . '_' . Author_Controller::LINK_TWITTER,
					'type'  => 'text',
				],
			],
			'conditional_logic' => [
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::GUEST_QUERY_TYPE,
						'operator' => '==',
						'value'    => self::GUEST_QUERY_MANUAL,
					],
				],
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::GUEST_QUERY_AUTO_OVERRIDE,
						'operator' => '==',
						'value'    => '1',
					],
				],
			],
		] ) );

		return $guests_repeater;
	}

	protected function get_show_countdown_field(): Field {
		return new Field( self::NAME . '_' . self::SHOW_COUNTDOWN, [
			'label'             => __( 'Show Countdown?', 'tribe' ),
			'type'              => 'true_false',
			'name'              => self::SHOW_COUNTDOWN,
			'default_value'     => 0,
			'ui'                => 1,
			'conditional_logic' => [
				[
					'field'    => 'field_' . self::NAME . '_' . self::STATE,
					'operator' => '==',
					'value'    => Post_State::UPCOMING,
				],
			],
		] );
	}

	protected function get_date_field(): Field {
		$timezone = wp_timezone_string();

		return new Field( self::NAME . '_' . self::DATE, [
			'label'             => __( 'Event Date', 'tribe' ),
			'type'              => 'date_picker',
			'name'              => self::DATE,
			'instructions'      => "In the timezone: <code>{$timezone}</code>",
			'display_format'    => 'm/d/Y',
			'return_format'     => 'Y-m-d',
			'conditional_logic' => [
				[
					'field'    => 'field_' . self::NAME . '_' . self::STATE,
					'operator' => '==',
					'value'    => Post_State::UPCOMING,
				],
			],
		] );
	}

	protected function get_time_field(): Field {
		$timezone = wp_timezone_string();

		return new Field( self::NAME . '_' . self::TIME, [
			'label'             => __( 'Event Time', 'tribe' ),
			'type'              => 'time_picker',
			'name'              => self::TIME,
			'instructions'      => "In the timezone: <code>{$timezone}</code>",
			'conditional_logic' => [
				[
					'field'    => 'field_' . self::NAME . '_' . self::STATE,
					'operator' => '==',
					'value'    => Post_State::UPCOMING,
				],
			],
		] );
	}

	protected function get_seat_count(): Field {
		return new Field( self::NAME . '_' . self::SEATS_LEFT, [
			'label'             => __( 'Number of Seats', 'tribe' ),
			'type'              => 'text',
			'name'              => self::SEATS_LEFT,
			'conditional_logic' => [
				[
					'field'    => 'field_' . self::NAME . '_' . self::STATE,
					'operator' => '==',
					'value'    => Post_State::UPCOMING,
				],
			],
		] );
	}

	protected function get_image_field(): Field {
		return new Field( self::NAME . '_' . self::IMAGE, [
			'label'             => __( 'Fallback Image', 'tribe' ),
			'type'              => 'image',
			'instructions'      => __( 'Image to display for logged out state instead of video.', 'tribe' ),
			'name'              => self::IMAGE,
			'return_format'     => 'id',
			'library'           => 'all',
			'conditional_logic' => [
				[
					'field'    => 'field_' . self::NAME . '_' . self::STATE,
					'operator' => '==',
					'value'    => Post_State::ON_DEMAND,
				],
			],
		] );
	}

	protected function get_description_field(): Field {
		return new Field( self::NAME . '_' . self::DESCRIPTION, [
			'label'        => __( 'Description', 'tribe' ),
			'type'         => 'wysiwyg',
			'name'         => self::DESCRIPTION,
			'toolbar'      => 'small',
			'media_upload' => true,
		] );
	}

	protected function get_big_marker_embed_field(): Field {
		return new Field( self::NAME . '_' . self::BIG_MARKER_EMBED, [
			'label'             => __( 'Event Registration Big Marker Embed', 'tribe' ),
			'type'              => 'textarea',
			'instructions'      => __( 'Include a Big Marker registration form for this upcoming event (this will overwrite any registration gravity form).', 'tribe' ),
			'name'              => self::BIG_MARKER_EMBED,
			'conditional_logic' => [
				[
					'field'    => 'field_' . self::NAME . '_' . self::STATE,
					'operator' => '==',
					'value'    => Post_State::UPCOMING,
				],
			],
		] );
	}

	private function get_gravity_form_field(): Field {
		$field = new Field( self::NAME . '_' . self::GRAVITY_FORM_EMBED );
		$field->set_attributes( [
			'label'             => __( 'Event Registration Gravity Form', 'tribe' ),
			'name'              => self::GRAVITY_FORM_EMBED,
			'type'              => 'select',
			'instructions'      => __( 'Include a Gravity Form as a registration form for this upcoming event.', 'tribe' ),
			'choices'           => $this->get_gravity_forms_choices(),
			'ui'                => 1,
			'allow_null'        => 1,
			'conditional_logic' => [
				[
					'field'    => 'field_' . self::NAME . '_' . self::STATE,
					'operator' => '==',
					'value'    => Post_State::UPCOMING,
				],
			],
		] );

		return $field;
	}

	protected function get_host_field(): Field {
		return new Field( self::NAME . '_' . self::HOST, [
			'label'         => __( 'Host', 'tribe' ),
			'instructions'  => __( 'Select the user who will be hosting this event.', 'tribe' ),
			'type'          => 'user',
			'name'          => self::HOST,
			'return_format' => 'array',
			'allow_null'    => true,
		] );
	}

	protected function get_hide_host_field(): Field {
		return new Field( self::NAME . '_' . self::HIDE_HOST, [
			'label'         => __( 'Hide Host?', 'tribe' ),
			'type'          => 'true_false',
			'name'          => self::HIDE_HOST,
			'default_value' => 0,
			'ui'            => 1,
		] );
	}

	protected function get_state_field(): Field {
		return new Field( self::NAME . '_' . self::STATE, [
			'label'    => __( 'Event State', 'tribe' ),
			'type'     => 'radio',
			'name'     => self::STATE,
			'required' => 1,
			'choices'  => [
				Post_State::UPCOMING  => __( 'Upcoming Event', 'tribe' ),
				Post_State::ON_DEMAND => __( 'On Demand', 'tribe' ),
			],
		] );
	}

	protected function get_transcript_field(): Field {
		return new Field( self::NAME . '_' . self::TRANSCRIPT, [
			'label'             => __( 'Transcript', 'tribe' ),
			'name'              => self::TRANSCRIPT,
			'type'              => 'file',
			'instructions'      => __( 'Include the html export from Descript to display a transcript for this video.', 'tribe' ),
			'required'          => 0,
			'return_format'     => 'array',
			'library'           => 'all',
			'min_size'          => '',
			'max_size'          => '',
			'mime_types'        => 'html',
			'conditional_logic' => [
				[
					'field'    => 'field_' . self::NAME . '_' . self::STATE,
					'operator' => '==',
					'value'    => Post_State::ON_DEMAND,
				],
			],
		] );
	}

	protected function get_video_field(): Field {
		return new Field( self::NAME . '_' . self::VIDEO, [
			'label'             => __( 'On Demand Video', 'tribe' ),
			'type'              => 'oembed',
			'instructions'      => __( 'Provide a link to the event video. Will only show to members with access.', 'tribe' ),
			'name'              => self::VIDEO,
			'conditional_logic' => [
				[
					'field'    => 'field_' . self::NAME . '_' . self::STATE,
					'operator' => '==',
					'value'    => Post_State::ON_DEMAND,
				],
			],
		] );
	}

	protected function get_duration_field(): Field_Group {
		$group = new Field_Group( self::NAME . '_' . self::DURATION, [
			'label'             => __( 'Duration', 'tribe' ),
			'name'              => self::DURATION,
			'wrapper'           => [
				'width' => '100',
			],
			'conditional_logic' => [
				[
					'field'    => 'field_' . self::NAME . '_' . self::STATE,
					'operator' => '==',
					'value'    => Post_State::ON_DEMAND,
				],
			],
		] );
		$group->add_field( new Field( self::NAME . '_' . self::DURATION_HOURS, [
			'label'   => __( 'Hours', 'tribe' ),
			'name'    => self::DURATION_HOURS,
			'type'    => 'number',
			'wrapper' => [
				'width' => '50',
			],
		] ) );
		$group->add_field( new Field( self::NAME . '_' . self::DURATION_MINS, [
			'label'   => __( 'Minutes', 'tribe' ),
			'name'    => self::DURATION_MINS,
			'type'    => 'number',
			'wrapper' => [
				'width' => '50',
			],
		] ) );

		return $group;
	}

	protected function get_upcoming_cta_field(): Field {
		return new Field( self::NAME . '_' . self::UPCOMING_CTA, [
			'label'        => __( 'Upcoming Event CTA', 'tribe' ),
			'type'         => 'text',
			'instructions' => __( 'When this event is displayed in a content loop, this text will display on the CTA button.', 'tribe' ),
			'name'         => self::UPCOMING_CTA,
		] );
	}

	protected function get_custom_sidebar_msg_field(): Field {
		return new Field( self::NAME . '_' . self::CUSTOM_SIDEBAR_MSG, [
			'label'        => __( 'Custom Message in Sidebar', 'tribe' ),
			'instructions' => __( 'A message to appear in sidebar if there is no form/countdown added.', 'tribe' ),
			'type'         => 'text',
			'name'         => self::CUSTOM_SIDEBAR_MSG,
		] );
	}

	protected function get_logged_out_msg_field(): Field {
		return new Field( self::NAME . '_' . self::LOGGED_OUT_MSG, [
			'label'        => __( 'Logged Out Message', 'tribe' ),
			'instructions' => __( 'A message to compel users without access to this content to sign up.', 'tribe' ),
			'type'         => 'text',
			'name'         => self::LOGGED_OUT_MSG,
		] );
	}

	protected function get_logged_out_link_field(): Field {
		return new Field( self::NAME . '_' . self::LOGGED_OUT_LINK, [
			'label'         => __( 'Logged Out Link', 'tribe' ),
			'type'          => 'link',
			'instructions'  => __( 'Link users without access to this content to a page where they can sign up.', 'tribe' ),
			'name'          => self::LOGGED_OUT_LINK,
			'return_format' => 'array',
		] );
	}

	private function get_show_membership_nav_field(): Field {
		$field = new Field( self::NAME . '_' . self::SHOW_MEMBERSHIP_NAV );
		$field->set_attributes( [
			'label'         => __( 'Show Membership Navigation?', 'tribe' ),
			'name'          => self::SHOW_MEMBERSHIP_NAV,
			'type'          => 'true_false',
			'default_value' => 1,
			'ui'            => 1,
		] );

		return $field;
	}

	private function get_content_gate_field(): Field {
		return new Field( self::NAME . '_' . self::CONTENT_GATE, [
			'label'         => __( 'Content Gate', 'tribe' ),
			'type'          => 'radio',
			'name'          => self::CONTENT_GATE,
			'required'      => 1,
			'default_value' => self::IS_PUBLIC_VIDEO,
			'choices'       => [
				self::IS_PUBLIC_VIDEO => __( 'Public Video', 'tribe' ),
				self::IS_FREE_ACCOUNT => __( 'Free Account Required', 'tribe' ),
				self::IS_PAID_ACCOUNT => __( 'Paid Account Required', 'tribe' ),
			],
		] );
	}

	private function get_sponsor_image_field(): Field {
		return new Field( self::NAME . '_' . self::SPONSOR_IMAGE, [
			'label'         => __( 'Sponsor Logo', 'tribe' ),
			'name'          => self::SPONSOR_IMAGE,
			'type'          => 'image',
			'return_format' => 'id',
		] );
	}

	private function get_sponsor_link_field(): Field {
		return new Field( self::NAME . '_' . self::SPONSOR_LINK, [
			'label' => __( 'Sponsor Link', 'tribe' ),
			'name'  => self::SPONSOR_LINK,
			'type'  => 'url',
		] );
	}
}
