<?php
declare( strict_types=1 );

namespace Tribe\Project\Object_Meta;

use Tribe\Libs\ACF;
use Tribe\Libs\ACF\Field;

class Ctas_Meta extends ACF\ACF_Meta_Group {

	public const NAME = 'ctas_meta';

	public const NEWSLETTER_LINK                    = 'newsletter_link';
	public const MEMBERSHIP_LINK                    = 'membership_link';
	public const ARTICLES_PODCASTS_NAV_LINK         = 'articles_podcasts_link';
	public const LISTICLES_NAV_LINK                 = 'listicles_link';
	public const SERVICES_LISTICLES_NAV_LINK        = 'services_listicles_link';
	public const PAGES_NAV_LINK                     = 'pages_link';
	public const ARTICLES_PODCASTS_NAV_MOBILE_LINK  = 'articles_podcasts_mobile_link';
	public const LISTICLES_NAV_MOBILE_LINK          = 'listicles_mobile_link';
	public const SERVICES_LISTICLES_NAV_MOBILE_LINK = 'services_listicles_mobile_link';
	public const PAGES_NAV_MOBILE_LINK              = 'pages_mobile_link';
	public const ARTICLES_PODCASTS_HEADER_CTA       = 'articles_podcasts_header_cta';
	public const LISTICLES_HEADER_CTA               = 'listicles_header_cta';
	public const SERVICES_LISTICLES_HEADER_CTA      = 'services_listicles_header_cta';
	public const PAGES_HEADER_CTA                   = 'pages_header_cta';

	public function get_keys(): array {
		return [
			static::NEWSLETTER_LINK,
			static::MEMBERSHIP_LINK,
			static::ARTICLES_PODCASTS_NAV_LINK,
			static::LISTICLES_NAV_LINK,
			static::SERVICES_LISTICLES_NAV_LINK,
			static::PAGES_NAV_LINK,
			static::ARTICLES_PODCASTS_NAV_MOBILE_LINK,
			static::LISTICLES_NAV_MOBILE_LINK,
			static::SERVICES_LISTICLES_NAV_MOBILE_LINK,
			static::PAGES_NAV_MOBILE_LINK,
			static::ARTICLES_PODCASTS_HEADER_CTA,
			static::LISTICLES_HEADER_CTA,
			static::SERVICES_LISTICLES_HEADER_CTA,
			static::PAGES_HEADER_CTA,
		];
	}

	public function get_value( $key, $post_id = 'option' ) {
		return parent::get_value( $post_id, $key );
	}

	public function get_group_config(): array {
		$group = new ACF\Group( self::NAME, $this->object_types );
		$group->set( 'title', __( 'CTAs Options', 'tribe' ) );

		$group->add_field( $this->get_newsletter_link_field() );
		$group->add_field( $this->get_membership_link_field() );
		$group->add_field( $this->get_articles_podcasts_nav_link_field() );
		$group->add_field( $this->get_listicles_nav_link_field() );
		$group->add_field( $this->get_services_listicles_nav_link_field() );
		$group->add_field( $this->get_pages_nav_link_field() );
		$group->add_field( $this->get_articles_podcasts_nav_link_mobile_field() );
		$group->add_field( $this->get_listicles_nav_link_mobile_field() );
		$group->add_field( $this->get_services_listicles_nav_link_mobile_field() );
		$group->add_field( $this->get_pages_nav_link_mobile_field() );
		$group->add_field( $this->get_articles_podcasts_header_cta_field() );
		$group->add_field( $this->get_listicles_header_cta_field() );
		$group->add_field( $this->get_services_listicles_header_cta_field() );
		$group->add_field( $this->get_pages_header_cta_field() );

		return $group->get_attributes();
	}

	private function get_newsletter_link_field(): Field {
		$field = new Field( self::NAME . '_' . self::NEWSLETTER_LINK );
		$field->set_attributes( [
			'label' => __( 'Default Link for Newsletters', 'tribe' ),
			'name'  => self::NAME . '_' . self::NEWSLETTER_LINK,
			'type'  => 'url',
		] );

		return $field;
	}

	private function get_membership_link_field(): Field {
		$field = new Field( self::NAME . '_' . self::MEMBERSHIP_LINK );
		$field->set_attributes( [
			'label' => __( 'Default Link for Membership', 'tribe' ),
			'name'  => self::NAME . '_' . self::MEMBERSHIP_LINK,
			'type'  => 'url',
		] );

		return $field;
	}

	private function get_articles_podcasts_nav_link_field(): Field {
		$field = new Field( self::NAME . '_' . self::ARTICLES_PODCASTS_NAV_LINK );
		$field->set_attributes( [
			'label'         => __( 'Default Link for Articles/Podcasts Scroll Nav CTA', 'tribe' ),
			'name'          => self::NAME . '_' . self::ARTICLES_PODCASTS_NAV_LINK,
			'type'          => 'link',
			'return_format' => 'array',
		] );

		return $field;
	}

	private function get_listicles_nav_link_field(): Field {
		$field = new Field( self::NAME . '_' . self::LISTICLES_NAV_LINK );
		$field->set_attributes( [
			'label'         => __( 'Default Link for Listicles Scroll Nav CTA', 'tribe' ),
			'name'          => self::NAME . '_' . self::LISTICLES_NAV_LINK,
			'type'          => 'link',
			'return_format' => 'array',
		] );

		return $field;
	}

	private function get_services_listicles_nav_link_field(): Field {
		$field = new Field( self::NAME . '_' . self::SERVICES_LISTICLES_NAV_LINK );
		$field->set_attributes( [
			'label'         => __( 'Default Link for Services Listicles Scroll Nav CTA', 'tribe' ),
			'name'          => self::NAME . '_' . self::SERVICES_LISTICLES_NAV_LINK,
			'type'          => 'link',
			'return_format' => 'array',
		] );

		return $field;
	}

	private function get_pages_nav_link_field(): Field {
		$field = new Field( self::NAME . '_' . self::PAGES_NAV_LINK );
		$field->set_attributes( [
			'label'         => __( 'Default Link for Pages Scroll Nav CTA', 'tribe' ),
			'name'          => self::NAME . '_' . self::PAGES_NAV_LINK,
			'type'          => 'link',
			'return_format' => 'array',
		] );

		return $field;
	}

	private function get_articles_podcasts_nav_link_mobile_field(): Field {
		$field = new Field( self::NAME . '_' . self::ARTICLES_PODCASTS_NAV_MOBILE_LINK );
		$field->set_attributes( [
			'label'         => __( 'Default Link in Mobile for Articles/Podcasts Scroll Nav CTA', 'tribe' ),
			'name'          => self::NAME . '_' . self::ARTICLES_PODCASTS_NAV_MOBILE_LINK,
			'type'          => 'link',
			'return_format' => 'array',
		] );

		return $field;
	}

	private function get_listicles_nav_link_mobile_field(): Field {
		$field = new Field( self::NAME . '_' . self::LISTICLES_NAV_MOBILE_LINK );
		$field->set_attributes( [
			'label'         => __( 'Default Link in Mobile for Listicles Scroll Nav CTA', 'tribe' ),
			'name'          => self::NAME . '_' . self::LISTICLES_NAV_MOBILE_LINK,
			'type'          => 'link',
			'return_format' => 'array',
		] );

		return $field;
	}

	private function get_services_listicles_nav_link_mobile_field(): Field {
		$field = new Field( self::NAME . '_' . self::SERVICES_LISTICLES_NAV_MOBILE_LINK );
		$field->set_attributes( [
			'label'         => __( 'Default Link in Mobile for Services Listicles Scroll Nav CTA', 'tribe' ),
			'name'          => self::NAME . '_' . self::SERVICES_LISTICLES_NAV_MOBILE_LINK,
			'type'          => 'link',
			'return_format' => 'array',
		] );

		return $field;
	}

	private function get_pages_nav_link_mobile_field(): Field {
		$field = new Field( self::NAME . '_' . self::PAGES_NAV_MOBILE_LINK );
		$field->set_attributes( [
			'label'         => __( 'Default Link in Mobile for Pages Scroll Nav CTA', 'tribe' ),
			'name'          => self::NAME . '_' . self::PAGES_NAV_MOBILE_LINK,
			'type'          => 'link',
			'return_format' => 'array',
		] );

		return $field;
	}

	private function get_articles_podcasts_header_cta_field(): Field {
		$field = new Field( self::NAME . '_' . self::ARTICLES_PODCASTS_HEADER_CTA );
		$field->set_attributes( [
			'label' => __( 'Default Link for Articles/Podcasts Header CTA', 'tribe' ),
			'name'  => self::NAME . '_' . self::ARTICLES_PODCASTS_HEADER_CTA,
			'type'  => 'link',
		] );

		return $field;
	}

	private function get_listicles_header_cta_field(): Field {
		$field = new Field( self::NAME . '_' . self::LISTICLES_HEADER_CTA );
		$field->set_attributes( [
			'label' => __( 'Default Link for Listicles Header CTA', 'tribe' ),
			'name'  => self::NAME . '_' . self::LISTICLES_HEADER_CTA,
			'type'  => 'link',
		] );

		return $field;
	}

	private function get_services_listicles_header_cta_field(): Field {
		$field = new Field( self::NAME . '_' . self::SERVICES_LISTICLES_HEADER_CTA );
		$field->set_attributes( [
			'label' => __( 'Default Link for Services Listicles Header CTA', 'tribe' ),
			'name'  => self::NAME . '_' . self::SERVICES_LISTICLES_HEADER_CTA,
			'type'  => 'link',
		] );

		return $field;
	}

	private function get_pages_header_cta_field(): Field {
		$field = new Field( self::NAME . '_' . self::PAGES_HEADER_CTA );
		$field->set_attributes( [
			'label' => __( 'Default Link for Pages Header CTA', 'tribe' ),
			'name'  => self::NAME . '_' . self::PAGES_HEADER_CTA,
			'type'  => 'link',
		] );

		return $field;
	}
}
