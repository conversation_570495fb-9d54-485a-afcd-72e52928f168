<?php

namespace Tribe\Project\Object_Meta;

use Tribe\Libs\ACF\ACF_Meta_Group;
use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Group;
use Tribe\Project\Post_Types\Member_Stream\Member_Stream;
use Tribe\Project\Templates\Components\Traits\Gravity_Forms_Choices;

class Member_Stream_Settings_Meta extends ACF_Meta_Group {
	use Gravity_Forms_Choices;

	public const NAME                     = 'tribe_member_stream_settings';
	public const ARCHIVE_TITLE            = Member_Stream::NAME . '_archive_page_title';
	public const AD_GROUP_HEADER_SCROLL   = 'ad_group_header_scroll';
	public const AD_GROUP_POST_HEADER     = 'ad_group_post_header';
	public const AD_GROUP_FOOTER          = 'ad_group_footer';
	public const FALLBACK_LOGGED_OUT_LINK = 'fallback_logged_out_link';
	public const FALLBACK_LOGGED_OUT_MSG  = 'fallback_logged_out_msg';
	/*public const SIDEBAR_MSG       		  = 'sidebar_msg'; */
	
	public const RESTRICTED_VIDEO_PAID_MESSAGE = 'restricted_video_paid_message';
	public const RESTRICTED_VIDEO_FREE_MESSAGE = 'restricted_video_free_message';

	public const SUBSCRIBE_FREE_ACCOUNT_TITLE	    = 'subscribe_free_account_form_title';
	public const SUBSCRIBE_FREE_ACCOUNT_DESCRIPTION = 'subscribe_free_account_form_description';
	public const SUBSCRIBE_FREE_ACCOUNT_GF_ID       = 'subscribe_free_account_gf_id';
	public const SUBSCRIBE_FREE_ACCOUNT_POPUP_IMAGE = 'subscribe_free_account_popup_image';

	public const SUBSCRIBE_PAID_ACCOUNT_BANNER_IMAGE  = 'subscribe_paid_account_banner_image';
	public const SUBSCRIBE_PAID_ACCOUNT_POPUP_IMAGE   = 'subscribe_paid_account_popup_image';
	public const SUBSCRIBE_PAID_ACCOUNT_TITLE         = 'subscribe_paid_account_title';
	public const SUBSCRIBE_PAID_ACCOUNT_BANNER_TITLE  = 'subscribe_paid_account_banner_title';
	public const SUBSCRIBE_PAID_ACCOUNT_DESCRIPTION   = 'subscribe_paid_account_description';
	public const SUBSCRIBE_PAID_ACCOUNT_BENEFITS      = 'subscribe_paid_account_benefits';
	public const SUBSCRIBE_PAID_ACCOUNT_BENEFITS_ITEM = 'subscribe_paid_account_benefits_item';
	public const SUBSCRIBE_PAID_ACCOUNT_CTA           = 'subscribe_paid_account_cta';

	protected function get_group_config(): array {
		$group = new Group( self::NAME, $this->object_types );
		$group->set( 'title', __( 'Member Stream Options', 'tribe' ) );
		$group->add_field( $this->get_archive_title_field() );
		$group->add_field( $this->get_ad_group_field_header() );
		$group->add_field( $this->get_ad_group_field_post() );
		$group->add_field( $this->get_ad_group_field_footer() );
		// $group->add_field( $this->get_sidebar_msg_field() );
		// $group->add_field( $this->get_fallback_logged_out_msg_field() );
		// $group->add_field( $this->get_fallback_logged_out_link_field() );
		$group->add_field( $this->get_restricted_video_paid_account_message_field() );
		$group->add_field( $this->get_restricted_video_free_account_message_field() );
		$group->add_field( $this->get_subscribe_free_account_title_field() );
		$group->add_field( $this->get_subscribe_free_account_description_field() );
		$group->add_field( $this->get_subscribe_free_account_gf_id_field() );
		$group->add_field( $this->get_subscribe_free_account_popup_image_field() );
		$group->add_field( $this->get_subscribe_paid_account_title_field() );
		$group->add_field( $this->get_subscribe_paid_account_banner_title_field() );
		$group->add_field( $this->get_subscribe_paid_account_description_field() );
		$group->add_field( $this->get_subscribe_paid_account_benefits_field() );
		$group->add_field( $this->get_subscribe_paid_account_cta_field() );
		$group->add_field( $this->get_subscribe_paid_account_banner_image_field() );
		$group->add_field( $this->get_subscribe_paid_account_popup_image_field() );

		return $group->get_attributes();
	}

	public function get_keys(): array {
		return [
			self::ARCHIVE_TITLE,
			self::AD_GROUP_HEADER_SCROLL,
			self::AD_GROUP_POST_HEADER,
			self::AD_GROUP_FOOTER,
			self::FALLBACK_LOGGED_OUT_LINK,
			self::FALLBACK_LOGGED_OUT_MSG,
			// self::SIDEBAR_MSG,
			self::SUBSCRIBE_FREE_ACCOUNT_TITLE,
			self::SUBSCRIBE_FREE_ACCOUNT_DESCRIPTION,
			self::SUBSCRIBE_FREE_ACCOUNT_GF_ID,
			self::SUBSCRIBE_FREE_ACCOUNT_POPUP_IMAGE,
			self::SUBSCRIBE_PAID_ACCOUNT_TITLE,
			self::SUBSCRIBE_PAID_ACCOUNT_DESCRIPTION,
			self::SUBSCRIBE_PAID_ACCOUNT_BENEFITS,
			self::SUBSCRIBE_PAID_ACCOUNT_CTA,
			self::SUBSCRIBE_PAID_ACCOUNT_POPUP_IMAGE,
			self::SUBSCRIBE_PAID_ACCOUNT_BANNER_IMAGE,
		];
	}

	protected function get_archive_title_field(): Field {
		return new Field( self::NAME . '_' . self::ARCHIVE_TITLE, [
			'label' => __( 'Archive Page Title', 'tribe' ),
			'name'  => self::ARCHIVE_TITLE,
			'type'  => 'text',
		] );
	}

	private function get_ad_group_field_header(): Field {
		$field = new Field( self::NAME . '_' . self::AD_GROUP_HEADER_SCROLL );
		$field->set_attributes( [
			'label'             => __( 'Header Scroll Ad Group', 'tribe' ),
			'name'              => self::NAME . '_' . self::AD_GROUP_HEADER_SCROLL,
			'type'              => 'taxonomy',
			'instructions'      => '',
			'required'          => 0,
			'conditional_logic' => 0,
			'taxonomy'          => 'ad-group',
			'field_type'        => 'select',
			'allow_null'        => 1,
			'add_term'          => 0,
			'save_terms'        => 0,
			'load_terms'        => 0,
			'return_format'     => 'id',
			'multiple'          => 0,
		] );

		return $field;
	}

	private function get_ad_group_field_post(): Field {
		$field = new Field( self::NAME . '_' . self::AD_GROUP_POST_HEADER );
		$field->set_attributes( [
			'label'             => __( 'Post Header Ad Group', 'tribe' ),
			'name'              => self::NAME . '_' . self::AD_GROUP_POST_HEADER,
			'type'              => 'taxonomy',
			'instructions'      => '',
			'required'          => 0,
			'conditional_logic' => 0,
			'taxonomy'          => 'ad-group',
			'field_type'        => 'select',
			'allow_null'        => 1,
			'add_term'          => 0,
			'save_terms'        => 0,
			'load_terms'        => 0,
			'return_format'     => 'id',
			'multiple'          => 0,
		] );

		return $field;
	}

	private function get_ad_group_field_footer(): Field {
		$field = new Field( self::NAME . '_' . self::AD_GROUP_FOOTER );
		$field->set_attributes( [
			'label'             => __( 'Footer Ad Group', 'tribe' ),
			'name'              => self::NAME . '_' . self::AD_GROUP_FOOTER,
			'type'              => 'taxonomy',
			'instructions'      => '',
			'required'          => 0,
			'conditional_logic' => 0,
			'taxonomy'          => 'ad-group',
			'field_type'        => 'select',
			'allow_null'        => 1,
			'add_term'          => 0,
			'save_terms'        => 0,
			'load_terms'        => 0,
			'return_format'     => 'id',
			'multiple'          => 0,
		] );

		return $field;
	}

	/*
	protected function get_sidebar_msg_field(): Field {
		return new Field( self::NAME . '_' . self::SIDEBAR_MSG, [
			'label' => __( 'Custom Message in Sidebar', 'tribe' ),
			'instructions' => __( 'A message to appear in sidebar if there is no form/countdown added.<br>This is used if the stream doesn\'t have a custom message set in Post Options.', 'tribe' ),
			'type'  => 'text',
			'name'  => self::SIDEBAR_MSG,
		] );
	}
	

	protected function get_fallback_logged_out_msg_field(): Field {
		return new Field( self::NAME . '_' . self::FALLBACK_LOGGED_OUT_MSG, [
			'label' => __( 'Fallback Logged Out Message', 'tribe' ),
			'instructions' => __( 'A message to compel users without access to this content to sign up.<br>This is used if the stream doesn\'t have a custom message.', 'tribe' ),
			'type'  => 'text',
			'name'  => self::FALLBACK_LOGGED_OUT_MSG,
		] );
	}

	protected function get_fallback_logged_out_link_field(): Field {
		return new Field( self::NAME . '_' . self::FALLBACK_LOGGED_OUT_LINK, [
			'label'         => __( 'Fallback Logged Out Link', 'tribe' ),
			'type'          => 'link',
			'instructions' => __( 'Link users without access to this content to a page where they can sign up.<br>This is used if the stream doesn\'t have a custom link.', 'tribe' ),
			'name'          => self::FALLBACK_LOGGED_OUT_LINK,
			'return_format' => 'array',
		] );
	}
	*/

	protected function get_subscribe_paid_account_banner_image_field(): Field {
		return new Field( self::NAME . '_' . self::SUBSCRIBE_PAID_ACCOUNT_BANNER_IMAGE, [
			'label'         => __( 'Subscribe with Paid Account Banner Image', 'tribe' ),
			'name'          => self::SUBSCRIBE_PAID_ACCOUNT_BANNER_IMAGE,
			'type'          => 'image',
			'return_format' => 'id',
			'instructions'  => __( 'Recommended image size: 800 x 800 px', 'tribe' ),
		] );
	}

	protected function get_subscribe_paid_account_popup_image_field(): Field {
		return new Field( self::NAME . '_' . self::SUBSCRIBE_PAID_ACCOUNT_POPUP_IMAGE, [
			'label'         => __( 'Subscribe with Paid Account Popup Image', 'tribe' ),
			'name'          => self::SUBSCRIBE_PAID_ACCOUNT_POPUP_IMAGE,
			'type'          => 'image',
			'return_format' => 'id',
			'instructions'  => __( 'Recommended image size: 800 x 800 px', 'tribe' ),
		] );
	}

	protected function get_subscribe_paid_account_title_field(): Field {
		return new Field( self::NAME . '_' . self::SUBSCRIBE_PAID_ACCOUNT_TITLE, [
			'label'         => __( 'Subscribe with Paid Account Title', 'tribe' ),
			'type'          => 'text',
			'name'          => self::SUBSCRIBE_PAID_ACCOUNT_TITLE,
			'default_value' => sprintf( __( 'Join %s Membership to get full access', 'tribe' ), get_bloginfo( 'name' ) ),
		] );
	}

	protected function get_subscribe_paid_account_banner_title_field(): Field {
		return new Field( self::NAME . '_' . self::SUBSCRIBE_PAID_ACCOUNT_BANNER_TITLE, [
			'label'         => __( 'Subscribe with Paid Account Banner Short Title', 'tribe' ),
			'type'          => 'text',
			'name'          => self::SUBSCRIBE_PAID_ACCOUNT_BANNER_TITLE,
			'default_value' => __( 'Start Your Transformation', 'tribe' ),
		] );
	}

	protected function get_subscribe_paid_account_description_field(): Field {
		return new Field( self::NAME . '_' . self::SUBSCRIBE_PAID_ACCOUNT_DESCRIPTION, [
			'label'         => __( 'Subscribe Short Description', 'tribe' ),
			'type'          => 'text',
			'name'          => self::SUBSCRIBE_PAID_ACCOUNT_DESCRIPTION,
			'default_value' => __( 'Get skilled, get confident and get connected so you can accelerate your career journey', 'tribe' ),
		] );
	}

	protected function get_subscribe_paid_account_benefits_field(): Field {
		return new Field( self::NAME . '_' . self::SUBSCRIBE_PAID_ACCOUNT_BENEFITS, [
			'label'        => __( 'Subscribe with Paid Account Benefits List', 'tribe' ),
			'name'         => self::SUBSCRIBE_PAID_ACCOUNT_BENEFITS,
			'type'         => 'repeater',
			'button_label' => 'Add Item',
			'sub_fields'   => [
				[
					'key'   => self::NAME . '_' . self::SUBSCRIBE_PAID_ACCOUNT_BENEFITS_ITEM,
					'label' => 'List Item',
					'name'  => self::SUBSCRIBE_PAID_ACCOUNT_BENEFITS_ITEM,
					'type'  => 'text',
				],
			],
		] );
	}

	protected function get_subscribe_paid_account_cta_field(): Field {
		return new Field( self::NAME . '_' . self::SUBSCRIBE_PAID_ACCOUNT_CTA, [
			'label'         => __( 'Subscribe with Paid Account Checkout Button', 'tribe' ),
			'type'          => 'link',
			'name'          => self::SUBSCRIBE_PAID_ACCOUNT_CTA,
			'return_format' => 'array',
		] );
	}

	protected function get_restricted_video_paid_account_message_field(): Field {
		return new Field( self::NAME . '_' . self::RESTRICTED_VIDEO_PAID_MESSAGE, [
			'label' 	    => __( 'Restricted Video Paid Account Message', 'tribe' ),
			'type'  	    => 'wysiwyg',
			'toolbar'       => 'basic',
			'media_upload'  => false,
			'name'  	    => self::RESTRICTED_VIDEO_PAID_MESSAGE,
			'instructions'  => __( 'This message will be displayed to users who do not have access to the video.', 'tribe' ),
			'default_value' => __( 'This video is available exclusively to <strong>members</strong>. Please log in or become a member to watch it.', 'tribe' ),
			'wrapper' => [
				'width' => '50',
			],
		] );
	}

	protected function get_restricted_video_free_account_message_field(): Field {
		return new Field( self::NAME . '_' . self::RESTRICTED_VIDEO_FREE_MESSAGE, [
			'label' 	    => __( 'Restricted Video Free Account Message', 'tribe' ),
			'type'  	    => 'wysiwyg',
			'toolbar'       => 'basic',
			'media_upload'  => false,
			'name'  	    => self::RESTRICTED_VIDEO_FREE_MESSAGE,
			'instructions'  => __( 'This message will be displayed to users who do not have an account and required to create one to see the video.', 'tribe' ),
			'default_value' => __( 'This video is available exclusively to <strong>registered users</strong>. Please log in or create an account to watch it.', 'tribe' ),
			'wrapper' => [
				'width' => '50',
			],
		] );
	}

	protected function get_subscribe_free_account_title_field(): Field {
		return new Field( self::NAME . '_' . self::SUBSCRIBE_FREE_ACCOUNT_TITLE, [
			'label'         => __( 'Subscribe with Free Account Title', 'tribe' ),
			'type'          => 'text',
			'name'          => self::SUBSCRIBE_FREE_ACCOUNT_TITLE,
			'default_value' => __( 'Create a free account to access this video', 'tribe' ),
		] );
	}

	protected function get_subscribe_free_account_description_field(): Field {
		return new Field( self::NAME . '_' . self::SUBSCRIBE_FREE_ACCOUNT_DESCRIPTION, [
			'label'         => __( 'Subscribe with Free Account Description', 'tribe' ),
			'type'          => 'text',
			'name'          => self::SUBSCRIBE_FREE_ACCOUNT_DESCRIPTION,
			'default_value' => __( 'Get access to exclusive content, live events and expert-curated resources.', 'tribe' ),
		] );
	}

	protected function get_subscribe_free_account_gf_id_field(): Field {
		return new Field( self::NAME . '_' . self::SUBSCRIBE_FREE_ACCOUNT_GF_ID, [
			'label' 	   => __( 'Subscribe with Free Account Gravity Form ID', 'tribe' ),
			'name'  	   => self::SUBSCRIBE_FREE_ACCOUNT_GF_ID,
			'type'         => 'select',
			'instructions' => __( 'This form will be used for the subscribe block inside the sidebar for not logged-in users.', 'tribe' ),
			'choices'      => $this->get_gravity_forms_choices(),
			'ui'           => 1,
			'allow_null'   => 1,
		] );
	}

	protected function get_subscribe_free_account_popup_image_field(): Field {
		return new Field( self::NAME . '_' . self::SUBSCRIBE_FREE_ACCOUNT_POPUP_IMAGE, [
			'label'         => __( 'Subscribe with Free Account Popup Image', 'tribe' ),
			'name'          => self::SUBSCRIBE_FREE_ACCOUNT_POPUP_IMAGE,
			'type'          => 'image',
			'return_format' => 'id',
			'instructions'  => __( 'Recommended image size: 800 x 800 px', 'tribe' ),
		] );
	}
}
