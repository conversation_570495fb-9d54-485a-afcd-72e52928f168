<?php declare( strict_types=1 );

namespace Tribe\Project\Object_Meta;

use MeprGroup;
use Tribe\Libs\ACF;
use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Repeater;
use Tribe\Project\Integrations\Memberpress\Memberpress;

class Membership_Meta extends ACF\ACF_Meta_Group {

	public const NAME = 'membership_meta';

	public const FORUM_LINK                      = 'membership_forum_link';
	public const GREETING                        = 'membership_greeting';
	public const MENU_MEMBERSHIP                 = 'membership_menu_membership';
	public const MENU_NAV                        = 'membership_menu_nav';
	public const MENU_SHOW_FORUM                 = 'membership_menu_show_forum';
	public const MENUS                           = 'membership_menus';
	public const SUPPORT_LINK                    = 'membership_support_link';
	public const MEMBERSHIP_GROUPS               = 'membership_groups';
	public const UNVERIFIED_GF_CREATED_USER_PAGE = 'membership_unverified_page';
	public const FREE_MEMBERSHIP         		 = 'membership_free';

	public function get_keys() {
		return [
			self::FORUM_LINK,
			self::GREETING,
			self::MENU_MEMBERSHIP,
			self::MENU_NAV,
			self::MENU_SHOW_FORUM,
			self::MENUS,
			self::SUPPORT_LINK,
			self::MEMBERSHIP_GROUPS,
			self::UNVERIFIED_GF_CREATED_USER_PAGE,
			self::FREE_MEMBERSHIP,
		];
	}

	public function get_value( $key, $post_id = 'option' ) {
		return parent::get_value( $post_id, $key );
	}

	/**
	 * Only show the fields if the MemberPress plugin is active.
	 *
	 * @return void
	 */
	public function register_group(): void {
		if ( ! Memberpress::is_active() ) {
			return;
		}
		parent::register_group();
	}

	public function get_group_config(): array {
		$group = new ACF\Group( self::NAME, $this->object_types );
		$group->set( 'title', __( 'Membership Settings', 'tribe' ) );
		$group->add_field( $this->get_greeting_field() );
		$group->add_field( $this->get_support_link_field() );
		$group->add_field( $this->get_forum_link_field() );
		$group->add_field( $this->get_membership_menus_repeater() );
		$group->add_field( $this->get_membership_groups() );
		$group->add_field( $this->get_unverified_redirect_page_field() );
		$group->add_field( $this->get_free_membership_field() );
		return $group->get_attributes();
	}

	protected function get_membership_groups(): Field {
		$field = new Field( self::NAME . '_' . self::MEMBERSHIP_GROUPS );
		$field->set_attributes( [
			'label'         => __( 'Membership Groups to show in Orders tab', 'tribe' ),
			'name'          => self::MEMBERSHIP_GROUPS,
			'type'          => 'post_object',
			'post_type'     => MeprGroup::$cpt,
			'multiple'      => true,
			'return_format' => 'id',
		] );

		return $field;
	}

	private function get_greeting_field(): Field {
		$field = new Field( self::NAME . '_' . self::GREETING );
		$field->set_attributes( [
			'label'             => __( 'Greeting', 'tribe' ),
			'name'              => self::GREETING,
			'type'              => 'text',
			'instructions'      => __( 'The greeting that displays for the user.', 'tribe' ),
			'required'          => 0,
			'conditional_logic' => 0,
			'default_value'     => '',
			'wrapper'           => [
				'width' => '',
				'class' => '',
				'id'    => '',
			],
			'placeholder'       => '',
			'prepend'           => '',
			'append'            => '',
			'maxlength'         => ''
		] );
		return $field;
	}

	private function get_forum_link_field(): Field {
		$field = new Field( self::NAME . '_' . self::FORUM_LINK );
		$field->set_attributes( [
			'label'             => __( 'Forum Link', 'tribe' ),
			'name'              => self::FORUM_LINK,
			'type'              => 'link',
			'required'          => 0,
			'conditional_logic' => 0,
			'wrapper'           => [
				'width' => '50',
				'class' => '',
				'id'    => '',
			],
			'return_format'     => 'array',
		] );
		return $field;
	}

	private function get_support_link_field(): Field {
		$field = new Field( self::NAME . '_' . self::SUPPORT_LINK );
		$field->set_attributes( [
			'label'   => __( 'Support Link', 'tribe' ),
			'name'    => self::SUPPORT_LINK,
			'type'    => 'link',
			'wrapper' => [
				'width' => '50',
				'class' => '',
				'id'    => '',
			],
			'return_format' => 'array',
		] );
		return $field;
	}

	private function get_membership_menus_repeater(): Repeater {
		$repeater = new Repeater( self::NAME . '_' . self::MENUS );
		$repeater->set_attributes( [
			'label'        => __( 'Membership Menus', 'tribe' ),
			'name'         => self::MENUS,
			'layout'       => 'table',
			'min'          => 0,
			'max'          => 0,
			'button_label' => __( 'Add Membership Menu', 'tribe' ),
			'instructions' => __( 'The first matching membership that the user is a part of will show the attached menu.', 'tribe' ),
		] );

		$membership_choices = $this->get_membership_choices();
		$repeater->add_field( new Field( self::MENU_MEMBERSHIP, [
			'label'         => __( 'Membership', 'tribe' ),
			'name'          => self::MENU_MEMBERSHIP,
			'type'          => 'select',
			'required'      => 1,
			'choices'       => $membership_choices,
			'default_value' => '',
			'allow_null'    => 0,
			'multiple'      => 0,
			'ui'            => 0,
			'wrapper' => [
				'width' => '40',
				'class' => '',
				'id'    => '',
			],
			'return_format' => 'value',
		] ) );

		$nav_choices = $this->get_menu_choices();
		$repeater->add_field( new field( self::MENU_NAV, [
			'label'         => __( 'Menu', 'tribe' ),
			'name'          => self::MENU_NAV,
			'type'          => 'select',
			'required'      => 1,
			'choices'       => $nav_choices,
			'default_value' => '',
			'allow_null'    => 0,
			'multiple'      => 0,
			'ui'            => 0,
			'wrapper' => [
				'width' => '40',
				'class' => '',
				'id'    => '',
			],
			'return_format' => 'value',
		] ) );

		$repeater->add_field( new Field( self::MENU_SHOW_FORUM, [
			'label'         => __( 'Show Forum Link', 'tribe' ),
			'name'          => self::MENU_SHOW_FORUM,
			'type'          => 'true_false',
			'required'      => 0,
			'conditional_logic' => 0,
			'default_value' => 0,
			'ui'            => 1,
			'ui_on_text'    => __( 'Yes', 'tribe' ),
			'ui_off_text'   => __( 'No', 'tribe' ),
			'wrapper' => [
				'width' => '20',
				'class' => '',
				'id'    => '',
			],
		] ) );
		return $repeater;
	}

	protected function get_unverified_redirect_page_field(): Field {
		$field = new Field( self::NAME . '_' . self::UNVERIFIED_GF_CREATED_USER_PAGE );
		$field->set_attributes( [
			'label'         => __( 'Unverified Gravity Forms created user account page redirection', 'tribe' ),
			'name'          => self::UNVERIFIED_GF_CREATED_USER_PAGE,
			'type'          => 'post_object',
			'post_type'     => 'page',
			'return_format' => 'id',
			'required'      => 0,
			'post_status' 	=> [
				0 => 'publish',
			],
		] );

		return $field;
	}

	protected function get_free_membership_field(): Field {
		$field = new Field( self::NAME . '_' . self::FREE_MEMBERSHIP );

		$field->set_attributes( [
			'label'         => __( 'Free Membership', 'tribe' ),
			'name'          => self::FREE_MEMBERSHIP,
			'instructions'  => __( 'This membership ID will be used on regwall and as the default gravity forms new user registration membership.', 'tribe' ),
			'type'          => 'post_object',
			'post_type'     => 'memberpressproduct',
			'return_format' => 'id',
			'required'      => 0,
			'allow_null'    => 1,
			'post_status' 	=> [
				0 => 'publish',
			],
		] );

		return $field;
	}

	/**
	 * Get all the created menus to be used for choices in the menu dropdown.
	 *
	 * @return array
	 */
	protected function get_menu_choices(): array {
		if ( ! is_admin() ) {
			return [];
		}
		$choices = [];
		$menus = get_terms( 'nav_menu' );
		if ( ! empty( $menus ) && ! is_wp_error( $menus ) ) {
			$choices = array_combine( wp_list_pluck( $menus, 'term_id' ), wp_list_pluck( $menus, 'name' ) );
		}
		return $choices;
	}

	/**
	 * Gathers all the membership choices to be used in the membership dropdown.
	 *
	 * @return array
	 */
	protected function get_membership_choices(): array {
		if ( ! is_admin() ) {
			return [];
		}
		$choices = [];
		if ( Memberpress::is_active() ) {
			$posts = \MeprCptModel::all( 'MeprProduct' );
			foreach ( $posts as $post ) {
				$choices[ $post->ID ] = $post->post_title;
			}
		}
		return $choices;
	}
}
