<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Listicle\ssl_Provider_Market_Fit;

use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Field_Section;
use Tribe\Project\Blocks\Lib\Block_Config_Json;
use Tribe\Libs\ACF\Repeater;
use Tribe\Project\Templates\Components\Traits\With_SSL_Provider_Fields;

class ssl_Provider_Market_Fit extends Block_Config_Json {
	use With_SSL_Provider_Fields;

	public const NAME                 = 'sslprovidermarketfit';
	public const SECTION_CONTENT      = 's-content';
	public const TITLE                = 'title';
	public const GOOD_FIT_TITLE       = 'good_fit_title';
	public const GOOD_FIT_DESCRIPTION = 'good_fit_description';
	public const GOOD_FIT_CASES       = 'good_fit_cases';
	public const BAD_FIT_TITLE        = 'bad_fit_title';
	public const BAD_FIT_DESCRIPTION  = 'bad_fit_description';
	public const BAD_FIT_CASES        = 'bad_fit_cases';
	public const CASE_TITLE           = 'case_title';
	public const CASE_DESCRIPTION     = 'case_description';

	public function add_block(): void {
		$this->set_block_json( self::NAME, [
			Block_Config_Json::COMPONENT_FILE_NAME => 'ssl_provider_market_fit',
			Block_Config_Json::LOAD_STYLE          => true,
		] );
	}

	public function get_good_fit_cases(): Repeater {
		$repeater = new Repeater( self::NAME . '_' . self::GOOD_FIT_CASES, [
			'label'        => __( 'Good fit Cases', 'tribe' ),
			'name'         => self::GOOD_FIT_CASES,
			'button_label' => __( 'Add new Case', 'tribe' ),
			'layout'       => 'row',
			'min'          => 0,
		] );
		$repeater->add_field( new field( self::NAME . '_' . self::CASE_TITLE, [
			'label' => __( 'Title', 'tribe' ),
			'name'  => self::CASE_TITLE,
			'type'  => 'text',
		] ) );

		$repeater->add_field( new field( self::NAME . '_' . self::CASE_DESCRIPTION, [
			'label'        => __( 'Description', 'tribe' ),
			'name'         => self::CASE_DESCRIPTION,
			'type'         => 'wysiwyg',
			'toolbar'      => 'basic',
			'media_upload' => 0,
		] ) );

		return $repeater;
	}

	public function get_bad_fit_cases(): Repeater {
		$repeater = new Repeater( self::NAME . '_' . self::BAD_FIT_CASES, [
			'label'        => __( 'Bad fit Cases', 'tribe' ),
			'name'         => self::BAD_FIT_CASES,
			'button_label' => __( 'Add new Case', 'tribe' ),
			'layout'       => 'row',
			'min'          => 0,
		] );
		$repeater->add_field( new field( self::NAME . '_' . self::CASE_TITLE, [
			'label' => __( 'Title', 'tribe' ),
			'name'  => self::CASE_TITLE,
			'type'  => 'text',
		] ) );

		$repeater->add_field( new field( self::NAME . '_' . self::CASE_DESCRIPTION, [
			'label'        => __( 'Description', 'tribe' ),
			'name'         => self::CASE_DESCRIPTION,
			'type'         => 'wysiwyg',
			'toolbar'      => 'basic',
			'media_upload' => 0,
		] ) );

		return $repeater;
	}

	public function add_fields(): void {
		$this->add_section( new Field_Section( self::SECTION_CONTENT, __( 'Content', 'tribe' ), 'accordion' ) )
			->add_field( new Field( self::NAME . '_' . self::TITLE, [
					'label' => __( 'Title', 'tribe' ),
					'name'  => self::TITLE,
					'type'  => 'text',
				] )
			)->add_field( new Field( self::NAME . '_' . self::GOOD_FIT_TITLE, [
					'label' => __( 'Good fit Title', 'tribe' ),
					'name'  => self::GOOD_FIT_TITLE,
					'type'  => 'text',
				] )
			)->add_field( new Field( self::NAME . '_' . self::GOOD_FIT_DESCRIPTION, [
					'label'        => __( 'Good fit Description', 'tribe' ),
					'name'         => self::GOOD_FIT_DESCRIPTION,
					'type'         => 'wysiwyg',
					'toolbar'      => 'basic',
					'media_upload' => 1,
				] )
			)->add_field(
				$this->get_good_fit_cases()
			)->add_field( new Field( self::NAME . '_' . self::BAD_FIT_TITLE, [
					'label' => __( 'Bad fit Title', 'tribe' ),
					'name'  => self::BAD_FIT_TITLE,
					'type'  => 'text',
				] )
			)->add_field( new Field( self::NAME . '_' . self::BAD_FIT_DESCRIPTION, [
					'label'        => __( 'Bad fit Description', 'tribe' ),
					'name'         => self::BAD_FIT_DESCRIPTION,
					'type'         => 'wysiwyg',
					'toolbar'      => 'basic',
					'media_upload' => 1,
				] )
			)->add_field(
				$this->get_bad_fit_cases()
			);
	}
}
