<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Pros_Cons;

use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Field_Section;
use Tribe\Project\Blocks\Lib\Block_Config_Json;
use Tribe\Project\Templates\Components\Traits\With_SSL_XvsY_Fields;

class ssl_XvsY_Pros_Cons extends Block_Config_Json {
	use With_SSL_XvsY_Fields;

	public const NAME = 'sslxvsyproscons';

	public const SECTION_CONTENT   = 's-content';
	public const INTRODUCTION_TEXT = 'intro_text';
	public const PRIMARY_CTA       = 'primary_cta';

	public const CTA_OPTION_GET_EXPERT_ADVICE_NO_UTM = 'cta_option_get_expert_advice_no_utm';
	public const CTA_OPTION_GET_CUSTOM_QUOTE_UTM     = 'cta_option_get_custom_quote_utm';
	public const CTA_OPTION_BOOK_DEMO_UTM            = 'cta_option_book_demo_utm';

	public function add_block(): void {
		$this->set_block_json( self::NAME, [
			Block_Config_Json::COMPONENT_FILE_NAME => 'ssl_xvsy_pros_cons',
			Block_Config_Json::LOAD_STYLE          => true,
		] );
	}

	protected function add_fields(): void {
		//==========================================
		// Content Fields
		//==========================================
		$this->add_section( new Field_Section( self::SECTION_CONTENT, __( 'Content', 'tribe' ), 'accordion' ) );

		$this->add_field( $this->get_ssl_xvsy_field() );

		$this->add_field( new Field( self::NAME . '_' . self::INTRODUCTION_TEXT, [
			'label'        => __( 'Introduction Text', 'tribe' ),
			'name'         => self::INTRODUCTION_TEXT,
			'type'         => 'wysiwyg',
			'toolbar'      => 'basic',
			'media_upload' => 0,
		] ) );

		$this->add_field( new Field( self::NAME . '_' . self::PRIMARY_CTA, [
			'label'         => __( 'Primary CTA', 'tribe' ),
			'name'          => self::PRIMARY_CTA,
			'type'          => 'select',
			'allow_null'    => 0,
			'ui'            => 1,
			'choices'       => [
				self::CTA_OPTION_GET_EXPERT_ADVICE_NO_UTM => __( 'Get Expert Advice: PPL Form without UTM', 'tribe' ),
				self::CTA_OPTION_GET_CUSTOM_QUOTE_UTM     => __( 'Get Custom Quote: PPL Form with UTM', 'tribe' ),
				self::CTA_OPTION_BOOK_DEMO_UTM            => __( 'Book Demo: PPL Form with UTM', 'tribe' ),
			],
			'default_value' => self::CTA_OPTION_GET_EXPERT_ADVICE_NO_UTM,
		] ) );
	}
}
