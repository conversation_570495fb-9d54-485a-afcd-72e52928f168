<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Video_Comparison;

use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Repeater;
use Tribe\Libs\ACF\Field_Section;
use Tribe\Project\Blocks\Lib\Block_Config_Json;
use Tribe\Project\Templates\Components\Traits\With_SSL_XvsY_Fields;

class ssl_XvsY_Video_Comparison extends Block_Config_Json {
	use With_SSL_XvsY_Fields;

	public const NAME = 'sslxvsyvideocomparison';

	public const SECTION_CONTENT   = 's-content';
	public const INTRODUCTION_TEXT = 'intro_text';
	public const VIDEO_THUMBNAIL   = 'video_thumbnail';
	public const VIDEO_URL         = 'video_url';
	public const LINKS_LIST        = 'links_list';
	public const SECTION_LINK      = 'section_link';

	public function add_block(): void {
		$this->set_block_json( self::NAME, [
			Block_Config_Json::COMPONENT_FILE_NAME => 'ssl_xvsy_video_comparison',
			Block_Config_Json::LOAD_STYLE          => true,
		] );
	}

	protected function add_fields(): void {
		//==========================================
		// Content Fields
		//==========================================
		$this->add_section( new Field_Section( self::SECTION_CONTENT, __( 'Content', 'tribe' ), 'accordion' ) );

		$this->add_field( $this->get_ssl_xvsy_field() );

		$this->add_field( new Field( self::NAME . '_' . self::INTRODUCTION_TEXT, [
			'label'        => __( 'Introduction Text', 'tribe' ),
			'name'         => self::INTRODUCTION_TEXT,
			'type'         => 'wysiwyg',
			'toolbar'      => 'basic',
			'media_upload' => 0,
		] ) );

		$this->add_field( new Field( self::NAME . '_' . self::VIDEO_THUMBNAIL, [
			'label'         => __( 'Video Thumbnail', 'tribe' ),
			'instructions'  => __( 'Thumbnail is only available for Youtube link.', 'tribe' ),
			'name'          => self::VIDEO_THUMBNAIL,
			'type'          => 'image',
			'required'      => 0,
			'return_format' => 'id',
		] ) );

		$this->add_field( new Field( self::NAME . '_' . self::VIDEO_URL, [
			'label'    => __( 'Youtube Video URL', 'tribe' ),
			'name'     => self::VIDEO_URL,
			'type'     => 'text',
			'required' => 1,
		] ) );

		$links_repeater = new Repeater( self::NAME . '_' . self::LINKS_LIST, [
			'label'        => __( 'See More Links', 'tribe' ),
			'name'         => self::LINKS_LIST,
			'button_label' => __( 'Add Link', 'tribe' ),
			'type'         => 'repeater',
			'layout'       => 'block',
			'required'     => 0,
		] );

		$links_repeater->add_field( new Field( self::NAME . '_' . self::LINKS_LIST . '_' . self::SECTION_LINK, [
			'label'    => __( 'Link', 'tribe' ),
			'name'     => self::SECTION_LINK,
			'required' => true,
			'type'     => 'link',
		] ) );

		$this->add_field( $links_repeater );
	}
}
