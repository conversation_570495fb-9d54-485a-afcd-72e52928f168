<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Overview;

use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Field_Section;
use Tribe\Project\Blocks\Lib\Block_Config_Json;
use Tribe\Project\Templates\Components\Traits\With_SSL_XvsY_Fields;

class ssl_XvsY_Overview extends Block_Config_Json {
	use With_SSL_XvsY_Fields;

	public const NAME = 'sslxvsyoverview';

	public const SECTION_CONTENT   = 's-content';
	public const INTRODUCTION_TEXT = 'intro_text';

	public function add_block(): void {
		$this->set_block_json( self::NAME, [
			Block_Config_Json::COMPONENT_FILE_NAME => 'ssl_xvsy_overview',
			Block_Config_Json::LOAD_STYLE          => true,
		] );
	}

	protected function add_fields(): void {
		//==========================================
		// Content Fields
		//==========================================
		$this->add_section( new Field_Section( self::SECTION_CONTENT, __( 'Content', 'tribe' ), 'accordion' ) );

		$this->add_field( $this->get_ssl_xvsy_field() );

		$this->add_field( new Field( self::NAME . '_' . self::INTRODUCTION_TEXT, [
			'label'        => __( 'Introduction Text', 'tribe' ),
			'name'         => self::INTRODUCTION_TEXT,
			'type'         => 'wysiwyg',
			'toolbar'      => 'basic',
			'media_upload' => 0,
			'required'     => false,
		] ) );
	}
}
