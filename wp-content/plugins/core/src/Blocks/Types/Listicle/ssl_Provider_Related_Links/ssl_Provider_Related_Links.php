<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Listicle\ssl_Provider_Related_Links;

use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Field_Section;
use Tribe\Project\Blocks\Lib\Block_Config_Json;
use Tribe\Project\Templates\Components\Traits\With_SSL_Provider_Fields;

class ssl_Provider_Related_Links extends Block_Config_Json {
	use With_SSL_Provider_Fields;

	public const NAME              = 'sslproviderrelatedlinks';
	public const SECTION_CONTENT   = 's-content';
	public const TITLE             = 'title';
	public const INTRODUCTION_TEXT = 'intro_text';

	public function add_block(): void {
		$this->set_block_json( self::NAME, [
			Block_Config_Json::COMPONENT_FILE_NAME => 'ssl_provider_related_links',
			Block_Config_Json::LOAD_STYLE          => true,
		] );
	}

	public function add_fields(): void {
		$this->add_section( new Field_Section( self::SECTION_CONTENT, __( 'Content', 'tribe' ), 'accordion' ) );
		$this->add_field( $this->get_ssl_provider_field() );
		$this->add_field( new Field( self::NAME . '_' . self::TITLE, [
			'label' => __( 'Title', 'tribe' ),
			'name'  => self::TITLE,
			'type'  => 'text',
		] ) );
		$this->add_field( new Field( self::NAME . '_' . self::INTRODUCTION_TEXT, [
			'label'        => __( 'Introduction Text', 'tribe' ),
			'name'         => self::INTRODUCTION_TEXT,
			'type'         => 'wysiwyg',
			'toolbar'      => 'basic',
			'media_upload' => 0,
		] ) );
	}
}
