<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Listicle\ssl_Provider_Alternatives;

use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Field_Section;
use Tribe\Project\Blocks\Lib\Block_Config_Json;
use Tribe\Project\Templates\Components\Traits\With_SSL_Provider_Fields;

class ssl_Provider_Alternatives extends Block_Config_Json {
	use With_SSL_Provider_Fields;

	public const NAME            = 'sslprovideralternatives';
	public const SECTION_CONTENT = 's-content';
	public const DESCRIPTION     = 'description';

	public function add_block(): void {
		$this->set_block_json( self::NAME, [
			Block_Config_Json::COMPONENT_FILE_NAME => 'ssl_provider_alternatives',
			Block_Config_Json::LOAD_STYLE          => true,
		] );
	}

	public function add_fields(): void {
		$this->add_section( new Field_Section( self::SECTION_CONTENT, __( 'Content', 'tribe' ), 'accordion' ) )
			->add_field( new Field( self::NAME . '_' . self::DESCRIPTION, [
					'label'        => __( 'Description', 'tribe' ),
					'name'         => self::DESCRIPTION,
					'type'         => 'wysiwyg',
					'toolbar'      => 'basic',
					'media_upload' => 0,
				] )
			);
	}
}
