<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Media_Text;

use Tribe\Project\Blocks\Types\Base_Model;
use Tribe\Project\Templates\Components\blocks\media_text\Media_Text_Block_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;

class Media_Text_Model extends Base_Model {
	/**
	 * @return array
	 */
	public function get_data(): array {
		return [
			Media_Text_Block_Controller::ATTRS                        => $this->get_attrs(),
			Media_Text_Block_Controller::CLASSES                      => $this->get_classes(),
			Media_Text_Block_Controller::LAYOUT                       => $this->get( Media_Text::LAYOUT, Media_Text::MEDIA_LEFT ),
			Media_Text_Block_Controller::MEDIA_STACK_INVERSE          => $this->get( Media_Text::MEDIA_STACK_INVERSE, false ),
			Media_Text_Block_Controller::CUSTOM_CLASSES_PRIMARY_CTA   => $this->get( Media_Text::CUSTOM_CLASSES_PRIMARY_CTA, '' ),
			Media_Text_Block_Controller::CUSTOM_CLASSES_SECONDARY_CTA => $this->get( Media_Text::CUSTOM_CLASSES_SECONDARY_CTA, '' ),
			Media_Text_Block_Controller::MEDIA_IMAGE_SIZE             => $this->get( Media_Text::MEDIA_IMAGE_SIZE, Media_Text::MEDIA_IMAGE_SIZE_REGULAR ),
			Media_Text_Block_Controller::WIDTH                        => $this->get( Media_Text::WIDTH, Media_Text::WIDTH_GRID ),
			Media_Text_Block_Controller::TITLE                        => $this->get( Media_Text::TITLE, '' ),
			Media_Text_Block_Controller::DESCRIPTION                  => $this->get( Media_Text::DESCRIPTION, '' ),
			Media_Text_Block_Controller::LIST                         => $this->get_list(),
			Media_Text_Block_Controller::CTA                          => $this->get_cta_args(),
			Media_Text_Block_Controller::CTA_ID                       => $this->get( Media_Text::CTA_ID, '' ),
			Media_Text_Block_Controller::SECONDARY_CTA                => $this->get_secondary_cta_args(),
			Media_Text_Block_Controller::SECONDARY_CTA_ID             => $this->get( Media_Text::SECONDARY_CTA_ID, '' ),
			Media_Text_Block_Controller::BUTTONS                      => $this->get( Media_Text::BUTTONS, [] ),
			Media_Text_Block_Controller::MEDIA_TYPE                   => $this->get( Media_Text::MEDIA_TYPE, '' ),
			Media_Text_Block_Controller::IMAGE                        => $this->get( Media_Text::IMAGE, 0 ),
			Media_Text_Block_Controller::VIDEO                        => $this->get( Media_Text::EMBED, '' ),
			Media_Text_Block_Controller::HTML                         => $this->get( Media_Text::HTML, '' ),
			Media_Text_Block_Controller::HEADING_TAG                  => $this->get( Media_Text::HEADING_TAG, Media_Text::HEADING_TAG_H2 ),
			Media_Text_Block_Controller::HEADING_TAG_H1               => $this->get( Media_Text::HEADING_TAG_H1, 'h1' ),
			Media_Text_Block_Controller::LOTTIE_DATA                  => $this->get( Media_Text::LOTTIE ),
		];
	}

	private function get_cta_args(): array {
		$cta = wp_parse_args( $this->get( Media_Text::CTA, [] ), [
			'title'  => '',
			'url'    => '',
			'target' => '',
		] );

		return [
			Link_Controller::CONTENT => $cta['title'],
			Link_Controller::URL     => $cta['url'],
			Link_Controller::TARGET  => $cta['target'],
		];
	}

	private function get_secondary_cta_args(): array {
		$cta = wp_parse_args( $this->get( Media_Text::SECONDARY_CTA, [] ), [
			'title'  => '',
			'url'    => '',
			'target' => '',
		] );

		return [
			Link_Controller::CONTENT => $cta['title'],
			Link_Controller::URL     => $cta['url'],
			Link_Controller::TARGET  => $cta['target'],
		];
	}

	private function get_list(): array {
		$list = $this->get( Media_Text::LIST ) ?: [];

		if ( $list ) {
			return array_map( function ( $list_row ) {
				return $list_row[ MEDIA_TEXT::LIST_ITEM ];
			}, $list );
		}

		return $list;
	}
}
