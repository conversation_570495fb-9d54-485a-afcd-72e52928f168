<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Media_Text;

use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Field_Section;
use Tribe\Libs\ACF\Repeater;
use Tribe\Project\Blocks\Lib\Block_Config_Json;
use Tribe\Project\Templates\Components\Traits\With_Lottie_Fields;

class Media_Text extends Block_Config_Json {
	use With_Lottie_Fields;

	public const NAME = 'mediatext';

	public const SECTION_SETTINGS             = 's-settings';
	public const LAYOUT                       = 'layout';
	public const MEDIA_LEFT                   = 'left';
	public const MEDIA_RIGHT                  = 'right';
	public const MEDIA_CENTER                 = 'center';
	public const MEDIA_LEFT_LIST              = 'left_list';
	public const MEDIA_RIGHT_LIST             = 'right_list';
	public const WIDTH                        = 'width';
	public const WIDTH_GRID                   = 'grid';
	public const WIDTH_FULL                   = 'full';
	public const WIDTH_CONTENT                = 'content';
	public const MEDIA_IMAGE_SIZE             = 'media_image_size';
	public const MEDIA_IMAGE_SIZE_REGULAR     = 'regular';
	public const MEDIA_IMAGE_SIZE_MEDIUM      = 'medium';
	public const MEDIA_IMAGE_SIZE_SMALL       = 'small';
	public const MEDIA_STACK_INVERSE          = 'media_stack_inverse';
	public const CUSTOM_CLASSES_PRIMARY_CTA   = 'custom_classes_primary_cta';
	public const CUSTOM_CLASSES_SECONDARY_CTA = 'custom_classes_secondary_cta';

	public const SECTION_CONTENT  = 's-content';
	public const LEAD_IN          = 'leadin';
	public const TITLE            = 'title';
	public const DESCRIPTION      = 'description';
	public const LIST             = 'list';
	public const LIST_ITEM        = 'list_item';
	public const CTA              = 'cta';
	public const CTA_ID           = 'cta_id';
	public const SECONDARY_CTA    = 'secondary_cta';
	public const SECONDARY_CTA_ID = 'secondary_cta_id';

	public const BUTTONS       = 'buttons';
	public const BUTTON_LINK   = 'button_link';
	public const BUTTON_CTA_ID = 'button_cta_id';
	public const BUTTON_STYLE  = 'button_style';

	public const STYLE_PRIMARY   = 'primary';
	public const STYLE_SECONDARY = 'secondary';
	public const STYLE_CTA       = 'cta';

	public const HEADING_TAG    = 'heading_tag';
	public const HEADING_TAG_H1 = 'h1';
	public const HEADING_TAG_H2 = 'h2';
	public const HEADING_TAG_H3 = 'h3';
	public const HEADING_TAG_H4 = 'h4';

	public const MEDIA_TYPE = 'media_type';
	public const IMAGE      = 'image';
	public const EMBED      = 'embed';
	public const HTML       = 'html';

	/**
	 * Register the block
	 */
	public function add_block(): void {
		$this->set_block_json( self::NAME, [
			Block_Config_Json::COMPONENT_FILE_NAME => 'media_text',
			Block_Config_Json::LOAD_STYLE          => true,
		] );
	}

	/**
	 * Register Fields for block
	 */
	public function add_fields(): void {
		//==========================================
		// Content Fields
		//==========================================
		$this->add_section( new Field_Section( self::SECTION_CONTENT, __( 'Content', 'tribe' ), 'accordion' ) )
			->add_field( new Field( self::NAME . '_' . self::TITLE, [
					'label' => __( 'Title', 'tribe' ),
					'name'  => self::TITLE,
					'type'  => 'text',
				] )
			)->add_field( new Field( self::NAME . '_' . self::DESCRIPTION, [
					'label'        => __( 'Description', 'tribe' ),
					'name'         => self::DESCRIPTION,
					'type'         => 'wysiwyg',
					'toolbar'      => 'basic',
					'media_upload' => 0,
				] )
			)->add_field( new Field( self::NAME . '_' . self::LIST, [
					'label'             => __( 'List of items', 'tribe' ),
					'name'              => self::LIST,
					'type'              => 'repeater',
					'button_label'      => 'Add Item',
					'sub_fields'        => [
						[
							'key'   => self::NAME . '_' . self::LIST_ITEM,
							'label' => 'List Item',
							'name'  => self::LIST_ITEM,
							'type'  => 'text',
						],
					],
					'conditional_logic' => [
						[
							[
								'field'    => 'field_' . self::NAME . '_' . self::LAYOUT,
								'operator' => '==',
								'value'    => self::MEDIA_LEFT_LIST,
							],
						],
						[
							[
								'field'    => 'field_' . self::NAME . '_' . self::LAYOUT,
								'operator' => '==',
								'value'    => self::MEDIA_RIGHT_LIST,
							],
						],
					],
				] )
			)->add_field( new Field( self::NAME . '_' . self::CTA, [
					'label' => __( 'Call to Action', 'tribe' ),
					'name'  => self::CTA,
					'type'  => 'link',
				] )
			)->add_field( new Field( self::NAME . '_' . self::CTA_ID, [
					'label' => __( 'CTA Button ID', 'tribe' ),
					'name'  => self::CTA_ID,
					'type'  => 'text',
				] )
			)->add_field( new Field( self::NAME . '_' . self::CUSTOM_CLASSES_PRIMARY_CTA, [
					'label' => __( 'Custom CSS classes for primary cta', 'tribe' ),
					'name'  => self::CUSTOM_CLASSES_PRIMARY_CTA,
					'type'  => 'text',
				] )
			)->add_field( new Field( self::NAME . '_' . self::SECONDARY_CTA, [
					'label' => __( 'Secondary Call to Action', 'tribe' ),
					'name'  => self::SECONDARY_CTA,
					'type'  => 'link',
				] )
			)->add_field( new Field( self::NAME . '_' . self::SECONDARY_CTA_ID, [
					'label' => __( 'Secondary CTA Button ID', 'tribe' ),
					'name'  => self::SECONDARY_CTA_ID,
					'type'  => 'text',
				] )
			)->add_field( new Field( self::NAME . '_' . self::CUSTOM_CLASSES_SECONDARY_CTA, [
					'label' => __( 'Custom CSS classes for secondary cta', 'tribe' ),
					'name'  => self::CUSTOM_CLASSES_SECONDARY_CTA,
					'type'  => 'text',
				] )
			)->add_field(
				$this->get_buttons_section()
			)->add_field( new Field( self::NAME . '_' . self::MEDIA_TYPE, [
					'label'         => __( 'Media Type', 'tribe' ),
					'name'          => self::MEDIA_TYPE,
					'type'          => 'radio',
					'choices'       => [
						self::IMAGE  => __( 'Image', 'tribe' ),
						self::EMBED  => __( 'Video oEmbed', 'tribe' ),
						self::HTML   => __( 'HTML', 'tribe' ),
						self::LOTTIE => __( 'Lottie Animation', 'tribe' ),
					],
					'default_value' => [
						self::IMAGE,
					],
				] )
			)->add_field( new Field( self::NAME . '_' . self::IMAGE, [
					'label'             => __( 'Image', 'tribe' ),
					'name'              => self::IMAGE,
					'type'              => 'image',
					'return_format'     => 'id',
					'preview_size'      => 'medium',
					'instructions'      => __(
						'Recommended image size by layout:<br>Center: 1920px wide with a 16:9 aspect ratio.<br>Left/Right: 1536px wide with a 4:3 aspect ratio.',
						'tribe'
					),
					'conditional_logic' => [
						[
							[
								'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
								'operator' => '==',
								'value'    => self::IMAGE,
							],
						],
					],
				] )
			)->add_field( new Field( self::NAME . '_' . self::EMBED, [
					'label'             => __( 'Video', 'tribe' ),
					'name'              => self::EMBED,
					'type'              => 'oembed',
					'conditional_logic' => [
						[
							[
								'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
								'operator' => '==',
								'value'    => self::EMBED,
							],
						],
					],
				] )
			)->add_field( new Field( self::NAME . '_' . self::HTML, [
					'label'             => __( 'Embed a podcast, indeed, calendly, etc.', 'tribe' ),
					'name'              => self::HTML,
					'type'              => 'textarea',
					'conditional_logic' => [
						[
							[
								'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
								'operator' => '==',
								'value'    => self::HTML,
							],
						],
					],
				] )
			)->add_field(
				$this->get_lottie_fields( self::NAME . '_' . self::MEDIA_TYPE, self::LOTTIE )
			)->add_field( new Field( self::NAME . '_' . self::HEADING_TAG, [
					'label'         => __( 'Heading Tag', 'tribe' ),
					'name'          => self::HEADING_TAG,
					'type'          => 'select',
					'ui'            => 1,
					'multiple'      => 0,
					'allow_null'    => 0,
					'choices'       => [
						self::HEADING_TAG_H1 => self::HEADING_TAG_H1,
						self::HEADING_TAG_H2 => self::HEADING_TAG_H2,
						self::HEADING_TAG_H3 => self::HEADING_TAG_H3,
						self::HEADING_TAG_H4 => self::HEADING_TAG_H4,
					],
					'default_value' => self::HEADING_TAG_H2,
				] )
			);

		//==========================================
		// Setting Fields
		//==========================================
		$this->add_section( new Field_Section( self::SECTION_SETTINGS, __( 'Settings', 'tribe' ), 'accordion' ) )
			->add_field( new Field( self::NAME . '_' . self::LAYOUT, [
				'label'         => __( 'Layout', 'tribe' ),
				'name'          => self::LAYOUT,
				'type'          => 'select',
				'choices'       => [
					self::MEDIA_LEFT       => __( 'LEFT: Media on the Left', 'tribe' ),
					self::MEDIA_CENTER     => __( 'CENTER: Media Centered', 'tribe' ),
					self::MEDIA_RIGHT      => __( 'RIGHT: Media on the Right', 'tribe' ),
					self::MEDIA_LEFT_LIST  => __( 'LEFT + LIST: Media on the Left with an unordered list', 'tribe' ),
					self::MEDIA_RIGHT_LIST => __( 'RIGHT + LIST: Media on the Left with an unordered list', 'tribe' ),
				],
				'default_value' => self::MEDIA_CENTER,
				'multiple'      => 0,
			] ) )->add_field( new Field( self::NAME . '_' . self::MEDIA_IMAGE_SIZE, [
					'label'             => __( 'Image Size', 'tribe' ),
					'name'              => self::MEDIA_IMAGE_SIZE,
					'type'              => 'select',
					'choices'           => [
						self::MEDIA_IMAGE_SIZE_REGULAR => __( 'Regular', 'tribe' ),
						self::MEDIA_IMAGE_SIZE_MEDIUM  => __( 'Medium', 'tribe' ),
						self::MEDIA_IMAGE_SIZE_SMALL   => __( 'Small', 'tribe' ),
					],
					'default_value'     => [
						self::MEDIA_IMAGE_SIZE_REGULAR,
					],
					'conditional_logic' => [
						[
							[
								'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
								'operator' => '==',
								'value'    => self::IMAGE,
							],
							[
								'field'    => 'field_' . self::NAME . '_' . self::LAYOUT,
								'operator' => '!=',
								'value'    => self::MEDIA_CENTER,
							],
						],
					],
				] )
			)->add_field( new Field( self::NAME . '_' . self::WIDTH, [
					'label'         => __( 'Width', 'tribe' ),
					'name'          => self::WIDTH,
					'type'          => 'select',
					'choices'       => [
						self::WIDTH_GRID    => __( 'Grid', 'tribe' ),
						self::WIDTH_CONTENT => __( 'Content', 'tribe' ),
					],
					'default_value' => [
						self::WIDTH_GRID,
					],
				] )
			)->add_field( new Field( self::NAME . '_' . self::MEDIA_STACK_INVERSE, [
					'label'             => __( 'Inverse the Media Stack Design', 'tribe' ),
					'name'              => self::MEDIA_STACK_INVERSE,
					'type'              => 'true_false',
					'default_value'     => 0,
					'ui'                => true,
					'ui_on_text'        => __( 'Inversed', 'tribe' ),
					'ui_off_text'       => __( 'Regular', 'tribe' ),
					'conditional_logic' => [
						[
							[
								'field'    => 'field_' . self::NAME . '_' . self::LAYOUT,
								'operator' => '==',
								'value'    => self::MEDIA_CENTER,
							],
						],
					],
				] )
			);
	}

	/**
	 * @return Repeater
	 */
	protected function get_buttons_section() {
		$group = new Repeater( self::NAME . '_' . self::BUTTONS );

		$group->set_attributes( [
			'label'        => __( 'List of Buttons', 'tribe' ),
			'name'         => self::BUTTONS,
			'instructions' => __( 'The buttons will only appear if there is no CTA in the block.', 'tribe' ),
			'layout'       => 'block',
			'min'          => 0,
			'max'          => 3,
			'button_label' => __( 'Add Button', 'tribe' ),
		] );

		$link = new Field( self::BUTTON_LINK, [
			'label' => __( 'Link', 'tribe' ),
			'name'  => self::BUTTON_LINK,
			'type'  => 'link',
		] );

		$group->add_field( $link );

		$button__id = new Field( self::BUTTON_CTA_ID, [
			'label' => __( 'CTA Button ID', 'tribe' ),
			'name'  => self::BUTTON_CTA_ID,
			'type'  => 'text',
		] );

		$group->add_field( $button__id );

		$button_style = new Field( self::NAME . '_' . self::BUTTON_STYLE, [
			'allow_null'    => 0,
			'choices'       => [
				self:: STYLE_PRIMARY   => __( 'Primary', 'tribe' ),
				self:: STYLE_SECONDARY => __( 'Secondary', 'tribe' ),
				self:: STYLE_CTA       => __( 'Text CTA', 'tribe' ),
			],
			'default_value' => self::STYLE_PRIMARY,
			'label'         => __( 'Style', 'tribe' ),
			'layout'        => 'vertical',
			'name'          => self::BUTTON_STYLE,
			'return_format' => 'value',
			'required'      => 0,
			'type'          => 'button_group',
		] );

		$group->add_field( $button_style );

		return $group;
	}

}
