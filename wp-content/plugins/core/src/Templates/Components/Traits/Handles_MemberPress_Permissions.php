<?php

namespace Tribe\Project\Templates\Components\Traits;

use <PERSON><PERSON><PERSON>\CrawlerDetect\CrawlerDetect;
use MeprProduct;
use MeprRule;
use MeprUser;
use MeprRulesCtrl;
use MeprSubscription;
use Tribe\Project\Integrations\Memberpress\Memberpress;
use Tribe\Project\Object_Meta\Member_Stream_Settings_Meta;
use Tribe\Project\Object_Meta\Post_Settings_Meta;
use Tribe\Project\Object_Meta\Page_Settings_Meta;
use Tribe\Project\Object_Meta\Service_Post_Settings_Meta;
use Tribe\Project\Object_Meta\Tool_Post_Settings_Meta;
use Tribe\Project\Object_Meta\Membership_Meta;
use Tribe\Project\Post_Types\Page\Page;
use Tribe\Project\Post_Types\Post\Post;
use Tribe\Project\Post_Types\Service_Post\Service_Post;
use Tribe\Project\Post_Types\Tool_Post\Tool_Post;
use Tribe\Project\Templates\Components\regwall_message\Regwall_Message_Controller;

trait Handles_MemberPress_Permissions {
	public const PAYWALL_PARAGRAPH_LIMIT = 3;

	private bool $is_paywall_opened = false;

	// Inside the post
	private const SHOW_REGWALL = 'show_regwall';

	// Inside the settings page
	private const REGWALL_RULE         = 'regwall_rule';
	private const REGWALL_TITLE        = 'regwall_title';
	private const REGWALL_DESCRIPTION  = 'regwall_description';
	private const REGWALL_SHOW_FORM    = 'regwall_show_form';
	private const REGWALL_GRAVITY_FORM = 'regwall_gravity_form';
	private const REGWALL_POST_TYPES   = [ Page::NAME, Post::NAME, Tool_Post::NAME ];

	public function is_user_authorized(): bool {
		if ( ! Memberpress::is_active() ) {
			return is_user_logged_in();
		}

		$current_user = wp_get_current_user();
		$mepr_user    = new MeprUser( $current_user->ID ?? 0 );

		return ! MeprRule::is_locked_for_user( $mepr_user, get_post() );
	}

	public function is_user_authorized_to_see( int $post_id ): bool {
		if ( ! Memberpress::is_active() ) {
			return true;
		}

		$current_user = wp_get_current_user();
		$mepr_user    = new MeprUser( $current_user->ID ?? 0 );

		return ! MeprRule::is_locked_for_user( $mepr_user, get_post( $post_id ) );
	}

	public function is_members_only(): bool {
		if ( ! Memberpress::is_active() ) {
			return false;
		}

		$mepr_user = new MeprUser( 0 );

		return MeprRule::is_locked_for_user( $mepr_user, get_post() );
	}

	public function current_user_has_paid_account(): bool {
		$current_user = wp_get_current_user();

		if ( ! $current_user || ! Memberpress::is_active() ) {
			return false;
		}

		if ( current_user_can( 'edit_posts' ) ) {
			return true;
		}

		$mepr_user = new MeprUser( $current_user->ID ?? 0 );

		if ( $mepr_user !== false && isset( $mepr_user->ID ) ) {
			$active_products = $mepr_user->active_product_subscriptions( 'products' );

			if ( $active_products ) {
				foreach ( $active_products as $product ) {
					if ( floatval( $product->price ) > 0 ) {
						return true;
					}
				}
			}
		}

		return false;
	}

	public function current_user_has_free_account(): bool {
		if ( $this->current_user_has_paid_account() ) {
			return true;
		}

		if ( current_user_can( 'edit_posts' ) ) {
			return true;
		}

		$current_user = wp_get_current_user();

		if ( ! $current_user || ! Memberpress::is_active() ) {
			return false;
		}

		$mepr_user = new MeprUser( $current_user->ID ?? 0 );

		if ( $mepr_user !== false && isset( $mepr_user->ID ) ) {
			$active_products = $mepr_user->active_product_subscriptions( 'ids' );
			$membership = (int) get_field( Membership_Meta::FREE_MEMBERSHIP, 'options' );

			if ( ! $membership ) {
				return false;
			}

			return ! empty( $active_products ) && in_array( $membership, $active_products );
		}

		return false;
	}

	/**
	 * @return mixed
	 * Get the setting field related with the post type passed
	 */
	private function get_regwall_setting_by_post_type( string $field_name, string $post_type ): mixed {
		if ( ! in_array( $post_type, self::REGWALL_POST_TYPES ) ) {
			return null;
		}

		$field_prefix = '';

		switch ( $post_type ) {
			case Page::NAME:
				$field_prefix = Page_Settings_Meta::NAME;
				break;
			case Tool_Post::NAME:
				$field_prefix = Tool_Post_Settings_Meta::NAME;
				break;
			case Service_Post::NAME:
				$field_prefix = Service_Post_Settings_Meta::NAME;
				break;
			default:
				$field_prefix = Post_Settings_Meta::NAME;
		}

		return get_field( $field_prefix . '_' . $field_name, 'option' );
	}

	/**
	 * @param int|null $post_id
	 *
	 * @return string|null
	 */
	public function get_post_regwall_rule( int|null $post_id = null ): string|null {
		if ( ! $this->is_regwall_active() || ! $this->is_regwall_enabled_for_post( $post_id ?: get_the_ID() ) ) {
			return null;
		}

		$post_type = get_post_type( $post_id ?: get_the_ID() );

		$rule_id = $this->get_regwall_setting_by_post_type( self::REGWALL_RULE, $post_type );

		if ( ! $rule_id ) {
			return null;
		}

		return $rule_id;
	}

	/**
	 * Get all Account Creation Gravity Forms 
	 * @return array
	 */
	public function get_all_account_creation_gf_ids(): array {
		$ids = [];

		foreach ( self::REGWALL_POST_TYPES as $post_type ) {
			$form_id = $this->get_regwall_setting_by_post_type( self::REGWALL_GRAVITY_FORM, $post_type );

			if ( $form_id && ! in_array( $form_id, $ids, true ) ) {
				$ids[] = $form_id;
			}
		}

		if ( $member_stream_gf_id = get_field( Member_Stream_Settings_Meta::SUBSCRIBE_FREE_ACCOUNT_GF_ID, 'option' ) ) {
			$ids[] = $member_stream_gf_id;
		}

		return $ids;
	}

	/**
	 * @param int $post_id
	 *
	 * @return bool
	 */
	public function is_content_visible_for_current_user( int $post_id ): bool {
		$rule_id = $this->get_post_regwall_rule( $post_id );

		// Bypass the paywall for crawlers
		$crawler_detect = new CrawlerDetect();

		if ( ! $rule_id || $crawler_detect->isCrawler() ) {
			return true;
		}

		// Here the MemberPress validates if the user can see the content based on the selected rule
		$paywall_html = MeprRulesCtrl::protect_shortcode_content( [
			'unauth'         => 'message',
			'rule'           => (string) $rule_id,
			'unauth_message' => '',
		], '', 'mepr-show' );

		// Return only a bool, overriding the default Memberpress Protection HTML returnal
		return ! $paywall_html;
	}

	/**
	 * @return boolean
	 * Verify if the regwall is active
	 */
	public function is_regwall_active() {
		if ( ! defined( 'REGWALL_ACTIVE' ) ) {
			return false;
		}

		return REGWALL_ACTIVE && Memberpress::is_active();
	}

	/**
	 * @return boolean
	 * Verify if the current post regwall is enabled
	 */
	public function is_regwall_enabled_for_post( int $post_id ) {
		return get_field( self::SHOW_REGWALL, $post_id );
	}

	/**
	 * @return string|null
	 * Verify if the current post has a regwall and returns a HTML block with the regwall info
	 */
	public function render_paywall_content_start(): string|null {
		// Validates if the Regwall is already created on the page and if the memberpress is active on the website
		if ( $this->is_paywall_opened || ! $this->is_regwall_active() || ! $this->is_regwall_enabled_for_post( get_the_ID() ) ) {
			return null;
		}

		$post_type = get_post_type( get_the_ID() );

		$args = [
			Regwall_Message_Controller::TITLE       => $this->get_regwall_setting_by_post_type( self::REGWALL_TITLE, $post_type ),
			Regwall_Message_Controller::DESCRIPTION => $this->get_regwall_setting_by_post_type( self::REGWALL_DESCRIPTION, $post_type ),
		];

		if ( $this->get_regwall_setting_by_post_type( self::REGWALL_SHOW_FORM, $post_type ) ) {
			$form_id = $this->get_regwall_setting_by_post_type( self::REGWALL_GRAVITY_FORM, $post_type );

			if ( $form_id ) {
				$args[ Regwall_Message_Controller::FORM_ID ] = $form_id;
			}
		}

		$regwall_message = defer_template_part(
			'components/regwall_message/regwall_message',
			null,
			$args
		);

		// Creates the regwall Memberpress custom container
		$paywall_html = '<div class="item-single__regwall mepr_error" data-post="'. get_the_ID() .'">'. $regwall_message .'</div>';

		// Opens a new content wrapper with a paywall class
		$paywall_html .= '<div class="item-single__regwall-protected t-sink s-sink l-sink">';

		$this->is_paywall_opened = true;

		return $paywall_html;
	}

	/**
	 * @return string|null
	 */
	public function render_paywall_content_end(): string|null {
		if ( ! $this->is_paywall_opened ) {
			return null;
		}

		// Closes the paywall tag
		return '</div>';
	}

	/**
	 * @return int
	 */
	public function get_paywall_paragraph_limit(): int {
		return self::PAYWALL_PARAGRAPH_LIMIT;
	}

	/**
	 * @return bool
	 * Verify if a string contains a memberpress shortcode
	 */
	public function has_memberpress_shortcodes( string $content ): bool {
		return str_contains( $content, '[mepr-' );
	}
}
