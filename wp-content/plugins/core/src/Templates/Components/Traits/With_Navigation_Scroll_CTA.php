<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\Traits;

use Tribe\Project\Object_Meta\Ctas_Meta;
use Tribe\Project\Object_Meta\Scroll_Nav_Cta_Meta;
use Tribe\Project\Post_Types\Media\Media;
use Tribe\Project\Post_Types\Post\Post;
use Tribe\Project\Post_Types\Service_Post\Service_Post;
use Tribe\Project\Post_Types\Tool_Post\Tool_Post;

trait With_Navigation_Scroll_CTA {
	/**
	 * Get scroll nav cta
	 * @return array
	 */
	public function get_default_scroll_nav_link(): array {
		$custom_cta = get_field( Scroll_Nav_Cta_Meta::CUSTOM_CTA );

		if ( $custom_cta ) {
			return $custom_cta;
		} else {
			if ( is_singular( Media::NAME ) || is_singular( Post::NAME ) ) {
				$scroll_nav_link = get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::ARTICLES_PODCASTS_NAV_LINK, 'option' );
			} elseif ( is_singular( Tool_Post::NAME ) ) {
				$scroll_nav_link = get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::LISTICLES_NAV_LINK, 'option' );
			} elseif ( is_singular( Service_Post::NAME ) ) {
				$scroll_nav_link = get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::SERVICES_LISTICLES_NAV_LINK, 'option' );
			} else {
				$scroll_nav_link = get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::PAGES_NAV_LINK, 'option' );
			}
		}

		if ( ! $scroll_nav_link ) {
			return [];
		}

		return $scroll_nav_link;
	}

	/**
	 * Get scroll nav cta in mobile variation
	 * @return array
	 */
	public function get_default_scroll_nav_mobile_link(): array {
		$custom_cta = get_field( Scroll_Nav_Cta_Meta::CUSTOM_MOBILE_CTA );

		if ( ! $custom_cta ) {
			$custom_cta = get_field( Scroll_Nav_Cta_Meta::CUSTOM_CTA );
		}

		if ( $custom_cta ) {
			return $custom_cta;
		} else {
			if ( is_singular( Media::NAME ) || is_singular( Post::NAME ) ) {
				$scroll_nav_link = get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::ARTICLES_PODCASTS_NAV_MOBILE_LINK, 'option' );
			} elseif ( is_singular( Tool_Post::NAME ) ) {
				$scroll_nav_link = get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::LISTICLES_NAV_MOBILE_LINK, 'option' );
			} elseif ( is_singular( Service_Post::NAME ) ) {
				$scroll_nav_link = get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::SERVICES_LISTICLES_NAV_MOBILE_LINK, 'option' );
			} else {
				$scroll_nav_link = get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::PAGES_NAV_MOBILE_LINK, 'option' );
			}
		}

		if ( ! $scroll_nav_link ) {
			return [];
		}

		return $scroll_nav_link;
	}
}
