<?php

namespace Tribe\Project\Theme\Config;

use Tribe\Project\Post_Types\Member_Stream\Member_Stream;
use Tribe\Project\Templates\Components\Traits\Authors_Links;

class ACF_Guest_Dynamic_Fields {
	use Authors_Links;

	public function enqueue(): void {
		global $post;

		// This makes sure we only enqueue the scripts on the correct pages.
		if ( ! $post || get_post_type( $post->ID ?? 0 ) !== Member_Stream::NAME ) {
			return;
		}

		wp_enqueue_script( 'acf-guest-dynamic-fields', get_theme_file_uri( '/assets/js/src/admin/core/acf-guest-dynamic-fields.js' ), [ 'acf-input' ], '1.0.0', true );
	}

	public function get_guest_data(): void {
		if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'acf_nonce' ) ) {
			exit();
		}

		$user_id = (int) sanitize_text_field( $_POST['guest'] ?? '' );
		$user    = get_user_by( 'id', $user_id );

		if ( ! $user ) {
			exit();
		}

		$social_links = $this->get_author_links( $user_id, [ 'url', 'linkedin', 'twitter' ] );

		$data = [
			'image'  => [
				'url' => get_avatar_url( $user_id ),
			],
			'name'   => $user->get( 'display_name' ),
			'title'  => $user->get( 'user_title' ),
			'bio'    => $user->get( 'user_description' ),
			'social' => [
				'linkedin' => $social_links[ 'linkedin' ] ?: '',
				'website'  => $social_links[ 'url' ] ?: '',
				'twitter'  => $social_links[ 'twitter' ] ?: '',
			],
		];

		echo json_encode( $data );

		exit();
	}
}
