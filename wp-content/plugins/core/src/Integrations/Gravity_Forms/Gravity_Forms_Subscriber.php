<?php
declare( strict_types=1 );

namespace Tribe\Project\Integrations\Gravity_Forms;

use Tribe\Libs\Container\Abstract_Subscriber;
use Tribe\Project\Integrations\Memberpress\Memberpress;

class Gravity_Forms_Subscriber extends Abstract_Subscriber {
	public function register(): void {
		add_filter( 'gform_confirmation_anchor', '__return_true' );
		add_filter( 'gform_tabindex', '__return_false' );
		add_filter( 'pre_option_rg_gforms_enable_html5', '__return_true' );
		add_filter( 'gform_enable_legacy_markup', '__return_false' );
		add_filter( 'gform_required_legend', '__return_empty_string' );

		add_filter( 'gform_form_theme_slug', function ( $slug, $form ) {
			return 'gravity-theme';
		}, 10, 2 );

		add_filter( 'gform_field_choice_markup_pre_render', function ( ...$args ) {
			return $this->container->get( Form_Markup::class )->customize_gf_choice_other( ... $args );
		}, 10, 4 );

		add_filter( 'gform_field_css_class', function ( ...$args ) {
			return $this->container->get( Form_Markup::class )->add_gf_select_field_class( ... $args );
		}, 10, 3 );

		add_action( 'gform_enqueue_scripts', function () {
			$this->container->get( Form_Styles::class )->enqueue_gravity_forms_jquery_ui_styles();
			$this->container->get( Form_Styles::class )->dequeue_gravity_forms_formsmain_styles();
		} );

		add_filter( 'gform_pre_render', function ( $form ) {
			return $this->container->get( Form_Styles::class )->deactivate_gf_animations( $form );
		} );

		add_filter( 'pre_option_rg_gforms_disable_css', function () {
			return $this->container->get( Form_Styles::class )->disable_gravity_forms_css();
		}, 10, 0 );

		add_filter( 'gform_validation_message', function ( $message, $form ) {
			return $this->container->get( Gravity_Forms::class )->gform_validation_message( $message, $form );
		}, 10, 2 );

		add_action( 'gform_save_field_value', function ( $value, $lead, $field, $form, $input_id ) {
			return $this->container->get( Gravity_Forms::class )->update_country_to_country_code( $value, $lead, $field, $form, $input_id );
		}, 10, 5 );

		add_filter( 'gform_form_tag', function ( $form_tag, $form ) {
			return $this->container->get( Gravity_Forms::class )->add_class_to_form( $form_tag, $form );
		}, 10, 2 );

		add_filter( 'gform_confirmation', function ( $confirmation, $form, $entry, $ajax ) {
			return $this->container->get( Form_Styles::class )->add_class_to_gpool_confirmation_message( $confirmation, $form, $entry, $ajax );
		}, 10, 4 );

		add_filter( 'gform_confirmation', function ( $confirmation, $form, $entry, $ajax ) {
			return $this->container->get( Gravity_Forms::class )->open_redirect_page_in_new_tab( $confirmation, $form, $entry, $ajax );
		}, 10, 4 );

		add_action( 'gform_after_submission', function ( $entry, $form ) {
			return $this->container->get( Gravity_Forms::class )->gravity_forms_after_submission( $entry, $form );
		}, 50, 2 );

		add_filter( 'gform_field_content', function ( $content, $field, $value, $lead_id, $form_id ) {
			return $this->container->get( Gravity_Forms::class )->add_template_options( $content, $field, $value, $lead_id, $form_id );
		}, 20, 5 );

		add_filter( 'gform_disable_notification', function ( $is_disabled, $notification, $form, $entry ) {
			return $this->container->get( Gravity_Forms::class )->disable_notifications_for_tech_team( $is_disabled, $notification, $form, $entry );
		}, 50, 4 );

		add_filter( 'gform_user_registered', function ( $user_id, $feed, $entry, $user_pass ) {
			$gf = $this->container->get( Gravity_Forms::class );

			if ( ! $gf->create_email_validation_meta_for_gf_created_user( $user_id, $feed ) ) {
				return false;
			}

			return $gf->assign_memberpress_role_to_gf_created_user( $user_id, $feed, $entry, $user_pass );
		}, 10, 4 );

		add_action( 'after_password_reset', function ( $wp_user, $new_pass ) {
			$gf = $this->container->get( Gravity_Forms::class );

			if ( $gf->is_gf_created_user_and_has_unverified_email( $wp_user->ID ) ) {
				return $gf->verify_gf_created_user_email( $wp_user->ID );
			}

			return false;
		}, 10, 2 );

		add_filter( 'gform_is_feed_asynchronous', function ( $is_asynchronous, $feed, $entry, $form ) {
			return $this->container->get( Gravity_Forms::class )->is_feed_asynchronous( $is_asynchronous, $feed, $entry, $form );
		}, 10, 4 );

		add_filter( 'gform_confirmation', function ( $confirmation, $form, $entry, $ajax ) {
			return $this->container->get( Gravity_Forms::class )->handle_gf_account_creation_confirmation( $confirmation, $form, $entry, $ajax );
		}, 10, 4 );

		add_action( 'gform_username', function ( $username, $feed, $form, $entry ) {
			return $this->container->get( Gravity_Forms::class )->sanitize_gf_created_username( $username );
		}, 10, 4 );

		add_action( 'template_redirect', function () {
			global $current_user;

			$gf = $this->container->get( Gravity_Forms::class );

			if ( Memberpress::is_active() && Memberpress::is_account_page() && $current_user && $gf->is_gf_created_user_and_has_unverified_email( $current_user->ID ) ) {
				$gf->redirect_unverified_gf_created_user();
				exit();
			}
		} );

		// Disable HubSpot tracking script on account pages
		add_filter( 'gform_hubspot_output_tracking_script', function () {
			if ( is_page('account') ) {
				return false;
			}

			return true;
		}, 9999 );

		add_filter( 'gform_disable_view_counter', '__return_true' );

		add_filter( 'pre_wp_mail', function ( $return, $args ) {
			return $this->container->get( Gravity_Forms::class )->disable_new_subscriber_email_for_admins( $return, $args );
		}, 10, 2);
	}
}
