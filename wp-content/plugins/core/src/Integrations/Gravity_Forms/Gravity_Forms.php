<?php

namespace Tribe\Project\Integrations\Gravity_Forms;

use GF_Fields;
use GFCommon;
use GFAPI;
use Tribe\Project\Taxonomies\Ssl_Crozdesk_Category\Ssl_Crozdesk_Category;
use Tribe\Project\Integrations\Gravity_Forms\Bwz_OAuth_Login;
use Tribe\Project\Integrations\Memberpress\Memberpress;
use Tribe\Project\Templates\Components\Traits\Handles_MemberPress_Permissions;
use Tribe\Project\Object_Meta\Membership_Meta;
use Tribe\Project\Object_Meta\Gravity_Form_Meta;
use function tad\WPBrowser\debug;

class Gravity_Forms {
	use Handles_MemberPress_Permissions;

	const COOKIE_NAME       = "bwz_forms";
	const SEPARATOR         = "_";
	const GF_EMAIL_VERIFIED = 'gf_email_verified';
	const GF_CREATED_USER   = 'gf_created_user';

	const TEMPLATE_SSL_CATEGORY_PPL_UNIT_QUESTION = "ssl_crozdesk_category_ppl_unit_question";

	public function gravity_forms_after_submission( $entry, $form ): void {
		if ( isset( $_COOKIE[ self::COOKIE_NAME ] ) ) {
			$cookie = htmlspecialchars( $_COOKIE[ self::COOKIE_NAME ] );

			$cookie_value = explode( self::SEPARATOR, $cookie );

			if ( in_array( $form['id'], $cookie_value ) ) {
				return;
			}

			$cookie_value[] = $form['id'];
			$cookie_value   = implode( self::SEPARATOR, $cookie_value );
		} else {
			$cookie_value = $form['id'];
		}

		setcookie( self::COOKIE_NAME, $cookie_value, time() + ( MONTH_IN_SECONDS ), "/" );
	}

	public function gform_validation_message( $message, $form ) {
		if ( gf_upgrade()->get_submissions_block() ) {
			return $message;
		}

		$message = "<div class='validation_error'><div class='validation_error-bg'></div><p>There were errors found in the information you submitted:</p>";
		$message .= '<ol>';

		foreach ( $form['fields'] as $field ) {
			if ( $field->failed_validation ) {
				$message .= sprintf( '<li>%s - %s</li>', GFCommon::get_label( $field ), $field->validation_message );
			}
		}

		$message .= '</ol></div>';

		return $message;
	}

	/*
	 * Update the Country Value of Address to Country Code before saving
	 */
	public function update_country_to_country_code( $value, $lead, $field, $form, $input_id ) {
		$state_input = $field->id . '.6';

		if ( $field->type === 'address' && $state_input === $input_id ) {
			$value = GF_Fields::get( 'address' )->get_country_code( $value );
		}

		return $value;
	}

	public function add_class_to_form( $form_tag, $form ) {
		if ( isset( $form['fields'] ) ) {
			$field_count = count( $form['fields'] );

			// Add multifield class to form when there are 2 fields or more
			if ( $field_count > 1 ) {
				$form_tag = str_replace( '<form', '<form class="gravity-form--with-multiple-fields"', $form_tag );
			}
		}

		return $form_tag;
	}

	public function add_template_options( $content, $field, $value, $lead_id, $form_id ) {
		$matches = [];

		// if str contains {{something}} using regex
		if ( preg_match( '/{{(.*?)}}/', $content, $matches ) ) {
			if ( isset( $matches[1] ) ) {
				if ( str_starts_with( $matches[1], self::TEMPLATE_SSL_CATEGORY_PPL_UNIT_QUESTION ) ) {
					[ $field_name, $term_id ] = explode( ":", $matches[1] );

					if ( ! isset( $term_id ) ) {
						$gppa_choices_filter_groups = $field['gppa-choices-filter-groups'];

						if ( isset( $gppa_choices_filter_groups ) && is_array( $gppa_choices_filter_groups ) ) {
							foreach ( $gppa_choices_filter_groups as $group ) {
								if ( is_array( $group ) ) {
									foreach ( $group as $item ) {
										if ( is_array( $item ) && isset( $item['property'] ) && $item['property'] === 'term_id' ) {
											if ( str_starts_with( $item['value'], 'gf_custom:' ) ) {
												$parts   = explode( ':', $item['value'] );
												$term_id = $parts[1] ?? null;
												break 2; // Exit both loops
											}
										}
									}
								}
							}
						}
					}

					if ( empty( $term_id ) ) {
						return $content;
					}

					$taxonomy_id = Ssl_Crozdesk_Category::NAME . "_$term_id";
					$question    = get_field( "BWZ_SSL_" . $field_name, $taxonomy_id );

					if ( ! $question ) {
						return $content;
					}

					$content = str_replace( $matches[0], $question, $content );
				}
			}
		}

		return $content;
	}

	public function disable_notifications_for_tech_team( $is_disabled, $notification, $form, $entry ) {

		// Remove notifications for Admin Notification when the form title contains 'Poll'
		if ( $notification['name'] === 'Admin Notification' && ( str_contains( $form['title'], 'Poll' ) || str_contains( $form['title'], 'poll' ) ) ) {
			return true;
		}

		return $is_disabled;
	}

	public function assign_memberpress_role_to_gf_created_user( $user_id, $feed, $entry, $user_pass ) {
		$membership_id = (int) get_field( Membership_Meta::FREE_MEMBERSHIP, 'option' );
		
		if ( ! $membership_id && class_exists( 'Bwz_OAuth_Login' ) ) {
			$membership_id = get_option( \Bwz_OAuth_Login::MEMBERSHIP_ID, 0 );
		}

		if ( $membership_id ) {
			if ( ! Memberpress::check_for_mepr_subscription( $membership_id, $user_id ) ) {
				Memberpress::create_mepr_subscription( $membership_id, $user_id );
			}
		}
	}

	public function create_email_validation_meta_for_gf_created_user( $user_id, array $gf_feed ): int|bool {
		add_user_meta( $user_id, self::GF_CREATED_USER, true );

		$gf_has_password_field = is_array( $gf_feed ) && isset( $gf_feed[ 'meta' ] ) && isset( $gf_feed[ 'meta' ][ 'password' ] ) && is_numeric( $gf_feed[ 'meta' ][ 'password' ] );

		// Bypass the email validation requirement if the Registration Feed has a Password field
		return add_user_meta( $user_id, self::GF_EMAIL_VERIFIED, $gf_has_password_field );
	}

	public function is_gf_created_user_and_has_unverified_email( $user_id ): bool {
		global $current_user;

		$is_gf_created_user        = get_user_meta( $user_id, self::GF_CREATED_USER, true );
		$is_gf_email_verified      = get_user_meta( $user_id, self::GF_EMAIL_VERIFIED, true );
		$is_user_already_activated = ! $current_user->user_activation_key;

		if ( $is_user_already_activated ) {
			if ( ! $is_gf_email_verified ) {
				update_user_meta( $user_id, self::GF_EMAIL_VERIFIED, true );
			}

			return false;
		}

		return $is_gf_created_user && ! $is_gf_email_verified;
	}

	public function verify_gf_created_user_email( $user_id ): bool {
		return (bool) update_user_meta( $user_id, self::GF_EMAIL_VERIFIED, true );
	}

	public function is_feed_asynchronous( $is_asynchronous, $feed, $entry, $form ) {
		$is_user_registration_form = isset( $form['template_id'] ) && $form['template_id'] === 'user_registration';
		$is_regwall_form           = isset( $form['id'] ) && in_array( $form['id'], $this->get_all_account_creation_gf_ids() );

		return ! ( $is_user_registration_form && $is_regwall_form );
	}

	public function handle_gf_account_creation_confirmation( $confirmation, $form, $entry, $ajax ) {
		$forms = $this->get_all_account_creation_gf_ids();

		if ( ! in_array( $form['id'], $forms ) ) {
			return $confirmation;
		}

		// Override other types of confirmation responses if necessary
		if ( ! is_string( $confirmation ) ) {
			$confirmation = "<div class=\"gform_confirmation_wrapper\"><div class=\"gform_confirmation_message\">Thank you for submitting your e-mail! This page will now refresh and you will be able to see the content.</div></div>";
		}

		// Adds the redirection with a timeout
		$confirmation .= "<script type=\"text/javascript\">setTimeout(function() { window.location.reload(true); }, 2000);</script>";

		return $confirmation;
	}

	public function redirect_unverified_gf_created_user() {
		$redirect_url = site_url();

		$page_to_redirect = get_field( Membership_Meta::UNVERIFIED_GF_CREATED_USER_PAGE, 'options' );

		if ( $page_to_redirect ) {
			$permalink = get_permalink( $page_to_redirect );

			if ( $permalink ) {
				$redirect_url = $permalink;
			}
		}

		wp_redirect( $redirect_url );
	}

	public function sanitize_gf_created_username( string $username ): string {
		$username_email = explode( '@', $username );

		$salt = wp_generate_password( 5, false, false );

		return preg_replace( "/[^a-z0-9 ]/", '', $username_email[0] ) . $salt;
	}

	public function open_redirect_page_in_new_tab( $confirmation, $form, $entry, $ajax ) {
		$user_registration_feed = GFAPI::get_feeds( null, $form['id'], 'gravityformsuserregistration', true );
		$is_user_registration   = is_wp_error( $user_registration_feed ) ? false : true;

		// Check if the confirmation is a redirect.
		if ( ! empty( $confirmation['redirect'] ) && ! $is_user_registration ) {
			$url                  = esc_url_raw( $confirmation['redirect'] );
			$confirmation_message = get_field( Gravity_Form_Meta::NAME . '_' . Gravity_Form_Meta::CONFIRMATION_MESSAGE, 'option' );

			if ( ! $confirmation_message ) {
				$confirmation_message = 'Thank you.';
			}

			// Modify the confirmation to open the redirect in a new tab.
			$confirmation = '<div id="gform_confirmation_wrapper_' . $form['id'] . '" data-form-theme="legacy" class="gform_confirmation_wrapper gform_legacy_markup_wrapper gform_legacy_confirmation_markup_wrapper gform-theme--no-framework "><div id="gform_confirmation_message_' . $form['id'] . '" class="gform_confirmation_message_' . $form['id'] . ' gform_confirmation_message">' . $confirmation_message . '</div></div>';
			$confirmation .= GFCommon::get_inline_script_tag( "window.open('$url', '_blank');" );
		}

		return $confirmation;
	}

	/**
	 * Disable the new user registration email for admins when the created user is a subscriber
	 *
	 * @param null|bool $return
	 * @param array     $args
	 *
	 * @return null|bool
	 */
	public function disable_new_subscriber_email_for_admins( null|bool $return, array $args ): null|bool {
		// Match the "New User Registration" subject
		if ( ! empty( $args['subject'] ) && ! empty( $args['message'] ) && str_contains( $args['subject'], 'New User Registration' ) ) {

			// Get the e-mail from the message body
			if ( preg_match( '/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/', $args['message'], $matches ) ) {
				$user = get_user_by( 'email', $matches[0] );

				// Disable the e-mail if the created user is a subscriber
				if ( $user && in_array( 'subscriber', $user->roles ) ) {
					return false;
				}
			}
		}

		return $return;
	}
}
