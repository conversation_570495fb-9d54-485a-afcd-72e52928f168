<?php declare( strict_types=1 );

namespace Tribe\Project\Integrations\Memberpress;

use Tribe\Libs\Container\Abstract_Subscriber;

class Memberpress_Subscriber extends Abstract_Subscriber {
	/**
	 * Register any specific memberpress hooks/actions here.
	 */
	public function register(): void {
		add_action( 'mepr_account_nav_content', function ( $action ) {
			$this->container->get( Memberpress_Account::class )->get_orders_tab_content( $action );
		} );

		add_action( 'wp_logout', function () {
			if ( defined( 'MEMBERPRESS_COOKIE_PATH' ) ) {
				setcookie( LOGGED_IN_COOKIE, ' ', time() - YEAR_IN_SECONDS, MEMBERPRESS_COOKIE_PATH, COOKIE_DOMAIN );
			}
		} );

		add_filter('mepr-validate-signup', function ( $errors ) {
			$new_error_message = sprintf(
				'This e-mail address has already been used for a site on the Black & White Zebra network. If you are an existing user of this site or any of our %spartner sites%s, please login using your credentials to complete your purchase.',
				'<a href="https://bwz.com/our-brands/" target="_blank"><strong>',
				'</strong></a>',
			);

			if ( isset( $errors[ 'user_email' ] ) ) {
				$errors['user_email'] = $new_error_message;
			}

			return $errors;
		} );

		add_action('wp_ajax_nopriv_memberpress_regwall_content_visible', function () {
			$this->container->get( Memberpress::class )->is_memberpress_regwall_content_visible();
		});

		add_action('wp_ajax_memberpress_regwall_content_visible', function () {
			$this->container->get( Memberpress::class )->is_memberpress_regwall_content_visible();
		});

		add_action( 'mepr_subscription_transition_status', function ( $old_status, $new_status, $subscription ) {
			$this->container->get( Memberpress::class )->maybe_enable_subscription_thank_you_redirect( $subscription );
		}, 10, 3 );

		add_action( 'template_redirect', function () {
			$this->container->get( Memberpress::class )->handle_paid_subscription_redirect_url();
		} );
	}
}
