<?php declare( strict_types=1 );

namespace Tribe\Project\Integrations\Memberpress;

use MeprSubscription;
use Tribe\Project\Post_Types\Download\Download;
use Tribe\Project\Post_Types\Member_Stream\Member_Stream;
use Tribe\Project\Templates\Components\routes\single\Single_Download_Controller;
use Tribe\Project\Templates\Components\routes\single\Single_Stream_Controller;
use Tribe\Project\Templates\Components\Traits\Handles_MemberPress_Permissions;
use MeprCptModel;
use MeprUser;
use MeprTransaction;
use Tribe\Project\Object_Meta\Member_Stream_Meta;
use Tribe\Project\Templates\Components\Traits\With_Get_Current_Url;

class Memberpress {
	use Handles_MemberPress_Permissions;
	use With_Get_Current_Url;

	public const PAID_SUBSCRIPTION_REDIRECT_URL = 'paid_subscription_redirect_url';

	/**
	 * checks to see if the Memberpress plugin is active.
	 *
	 * @return bool
	 */
	public static function is_active(): bool {
		return class_exists( 'MeprUser' );
	}

	/**
	 * checks to see if the current page is the Memberpress account page.
	 *
	 * @return bool
	 */
	public static function is_account_page(): bool {
		global $post;

		return MeprUser::is_account_page( $post );
	}

	/**
	 * @see \MeprRulesCtrl if(!MeprHooks::apply_filters('mepr-pre-run-rule-content', true, $current_post, $uri))
	 *                     MemberPress uses this filter to figure out if the current post needs to be protected or not.
	 *                     If we return "false", then the conditional will resolve to "true" and the original content
	 */
	public function allow_blocks_to_be_rendered_in_cpts( $current_post ) {
		switch ( get_post_type( $current_post ) ) {
			case Download::NAME:
				return Single_Download_Controller::ENABLE_MEMBERPRESS_FILTER;
			case Member_Stream::NAME:
				return Single_Stream_Controller::ENABLE_MEMBERPRESS_FILTER;
			default:
				return true;
		}
	}

	/**
	 * Format all the available rules to ACF choice options, used in ACF 'select' field types
	 *
	 * @return array
	 */
	public static function get_available_rules_for_acf_choices(): array {
		if ( ! is_admin() ) {
			return [];
		}

		$rules = MeprCptModel::all( 'MeprRule' );

		if ( empty( $rules ) ) {
			return [];
		}

		$rules_formatted = [];

		foreach ( $rules as $rule ) {
			$rules_formatted[ $rule->ID ] = $rule->post_title;
		}

		return $rules_formatted;
	}

	/*
	* Verify if the current post has a regwall enabled and if the content is visible for the current user
	*/
	public function is_memberpress_regwall_content_visible() {
		$post_id = (int) sanitize_text_field( $_GET['post_id'] );

		if ( ! $post_id ) {
			wp_send_json_error( 'Invalid post id' );
			wp_die();
		}

		wp_send_json_success( [
			'visible' => $this->is_content_visible_for_current_user( $post_id ),
		], 200 );

		wp_die();
	}

	public static function check_for_mepr_subscription( $membership_id, $user_id ): ?bool {
		if ( ! self::is_active() ) {
			return null;
		}

		if ( ! is_user_logged_in() ) {
			return null;
		}

		$mepr_user = new MeprUser( $user_id );

		if ( $mepr_user->is_active() && $mepr_user->is_already_subscribed_to( $membership_id ) ) {
			return true;
		}

		return false;
	}

	public static function create_mepr_subscription( $membership_id, $user_id ): void {
		// Create a new subscription
		$sub              = new MeprSubscription();
		$sub->user_id     = $user_id;
		$sub->product_id  = $membership_id;
		$sub->period_type = 'lifetime';
		$sub->status      = MeprSubscription::$active_str;
		$sub->created_at  = gmdate( 'Y-m-d H:i:s' );
		$sub->store();

		// Create a new transaction
		$txn                  = new MeprTransaction();
		$txn->user_id         = $user_id;
		$txn->product_id      = $membership_id;
		$txn->status          = MeprTransaction::$complete_str;
		$txn->txn_type        = MeprTransaction::$payment_str;
		$txn->subscription_id = $sub->id;
		$txn->store();
	}

	/**
	 * @return string|null
	 */
	private function get_paid_subscription_redirect_url_cookie() : ?string {
		return (string) $_COOKIE[ self::PAID_SUBSCRIPTION_REDIRECT_URL ] ?: null;
	}

	/**
	 * @return string|null
	 */
	private function get_paid_subscription_redirect_url_meta() : ?string {
		return (string) get_user_meta( get_current_user_id(), self::PAID_SUBSCRIPTION_REDIRECT_URL, true ) ?: null;
	}

	/**
	 * @return void
	 */
	private function delete_paid_subscription_redirect_data() : void {
		setcookie( self::PAID_SUBSCRIPTION_REDIRECT_URL, '', time() - 3600, '/' );
		delete_user_meta( get_current_user_id(), self::PAID_SUBSCRIPTION_REDIRECT_URL );
	}

	/**
	 * @return void
	 */
	public function handle_paid_subscription_redirect_url() : void {
		// Redirects the user after the checkout is completed
		if ( $redirect_url = $this->get_paid_subscription_redirect_url_meta() ) {
			$this->delete_paid_subscription_redirect_data();
			wp_redirect( $redirect_url );
			exit;
		}

		// Apply the redirect url when it is a single stream post that has a paid content gate or paid resources
		if ( is_singular( Member_Stream::NAME ) ) {
			$content_gate = (string) get_field( Member_Stream_Meta::CONTENT_GATE ) ?? null;
			$resources    = get_field( Member_Stream_Meta::RESOURCES );

			$should_set_cookie = $content_gate === Member_Stream_Meta::IS_PAID_ACCOUNT && ! $this->current_user_has_paid_account();

			if ( $resources ) {
				foreach ( $resources as $resource ) {
					$access_level = (string) $resource[ Member_Stream_Meta::RESOURCE_ACCESS_LEVEL ] ?? null;

					if ( $access_level === Member_Stream_Meta::ACCESS_LEVEL_PAID && ! $this->current_user_has_paid_account() ) {
						$should_set_cookie = true;

						break;
					}
				}
			}

			if ( $should_set_cookie ) {
				setcookie( self::PAID_SUBSCRIPTION_REDIRECT_URL, $this->get_current_url( false ), time() + MONTH_IN_SECONDS, "/" );
			}

			return;
		}

		// Remove the Paid Subscription redirect url when the user access the home page or singles
		if ( ( is_single() || is_front_page() ) && $this->get_paid_subscription_redirect_url_cookie() ) {
			$this->delete_paid_subscription_redirect_data();
			return;
		}
	}

	public function maybe_enable_subscription_thank_you_redirect( $subscription ) : void {
		$redirect_url = $this->get_paid_subscription_redirect_url_cookie();

		if ( $redirect_url && $subscription->status === 'active' && $subscription->price > 0 && is_user_logged_in() ) {
			update_user_meta( get_current_user_id(), self::PAID_SUBSCRIPTION_REDIRECT_URL, esc_url_raw( $redirect_url ) );
		}
	}
}
